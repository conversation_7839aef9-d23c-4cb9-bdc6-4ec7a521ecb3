version: v1
build:
  os: centos7
  tools:
    mt_oraclejdk: 8
    maven: 3.9.5
    mt_rasp:
  deps:
  - dc_python: 3.7
  - dc_python: 3.11
  run:
    workDir: ./
    cmd:
    - sh deploy/compile.sh
  target:
    distDir: ./predictor-service/target/
    files:
    - ./*.jar
    - ../../deploy
    - ./classes/python
autodeploy:
  hulkos: centos7
  tools:
    mt_oraclejdk: 8
    mt_rasp:
  deps:
  - dc_python: 3.7
  - dc_python: 3.11
  targetDir: /opt/meituan/com.sankuai.algoplatform.predictor/
  run: sh deploy/run.sh
  check: sh deploy/check.sh
  checkRetry: 5
  CheckInterval: 60s
