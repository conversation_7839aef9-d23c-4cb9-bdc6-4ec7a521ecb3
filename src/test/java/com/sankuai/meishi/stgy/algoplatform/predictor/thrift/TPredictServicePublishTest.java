package com.sankuai.meishi.stgy.algoplatform.predictor.thrift;

import com.sankuai.meishi.stgy.algoplatform.common.util.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.config.BmlMatchMafkaBeanConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.processor.ProducerProcessor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtils.class, RaptorTrack.class})
public class TPredictServicePublishTest {

    @InjectMocks
    private TPredictServicePublish tPredictServicePublish;

    @Mock
    private ProducerProcessor predictResultProducer;

    @Mock
    private BmlMatchMafkaBeanConfig bmlMatchMafkaBeanConfig;

    @Mock
    private ApplicationContext applicationContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(SpringContextUtils.class);
        PowerMockito.mockStatic(RaptorTrack.class);
    }

    @Test
    public void testSendResultMsg_WithResultSendCellName() {
        // Setup
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", "test-cell");
        String message = "test-message";

        // Mock
        when(predictResultProducer.send(anyString(), anyString())).thenReturn(true);

        // Execute
        boolean result = tPredictServicePublish.sendResultMsg(extra, message);

        // Verify
        assertTrue(result);
        verify(predictResultProducer).send("test-cell", "test-message");
    }

    @Test
    public void testSendResultMsg_WithBeanConfig() {
        // Setup
        Map<String, String> extra = new HashMap<>();
        String message = "test-message";
        String beanName = "test-bean";

        // Mock
        when(bmlMatchMafkaBeanConfig.getProducerBeanName()).thenReturn(beanName);
        when(SpringContextUtils.getBean(beanName)).thenReturn(predictResultProducer);
        when(predictResultProducer.send(anyString(), anyString())).thenReturn(true);

        // Execute
        boolean result = tPredictServicePublish.sendResultMsg(extra, message);

        // Verify
        assertTrue(result);
        verify(predictResultProducer).send("default-cell", "test-message");
    }

    @Test
    public void testSendResultMsg_WithNullProcessor() {
        // Setup
        Map<String, String> extra = new HashMap<>();
        String message = "test-message";
        String beanName = "test-bean";

        // Mock
        when(bmlMatchMafkaBeanConfig.getProducerBeanName()).thenReturn(beanName);
        when(SpringContextUtils.getBean(beanName)).thenReturn(null);

        // Execute
        boolean result = tPredictServicePublish.sendResultMsg(extra, message);

        // Verify
        assertFalse(result);
        verify(RaptorTrack.class);
        RaptorTrack.reportUnexpectedVisit("TPredictServicePublish.sendResultMsg", "ProducerProcessor is null");
    }
}