# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

### rules
永远用中文回复我

## Build and Test Commands

### Build
```bash
mvn clean install
```

### Run Tests
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=JavaBridgeInvokeTest

# Run specific test method
mvn test -Dtest=JavaBridgeInvokeTest#testPigeonInvoke_Success

# Run factory-related tests
mvn test -Dtest=*Factory*Test,*Config*Test
```

### Generate Test Report
```bash
mvn surefire-report:report
```

## High-Level Architecture

### Key Components
1. **JavaBridge**: Handles `pigeonInvoke` and `thriftInvoke` methods for service calls.
2. **PigeonServiceFactory**: Manages Pigeon service proxy creation and caching.
3. **ThriftClientProxyBeanConfig**: Manages Thrift service proxy creation and caching.

### Testing Framework
- **JUnit 4**: Test framework.
- **Mockito**: Mocking framework.
- **PowerMock**: For mocking static methods.
- **FastJSON**: JSON processing.

### Test Coverage
- **pigeonInvoke**: Covers normal calls, JSON errors, proxy failures, and edge cases.
- **thriftInvoke**: Covers normal calls, JSON errors, proxy failures, and edge cases.
- **Factory Tests**: Covers proxy creation, caching, and resource cleanup.

### Test Scripts
- `run_invoke_tests.sh`: Runs all `JavaBridge`-related tests.
- `run_factory_tests.sh`: Runs all factory-related tests.

### Important Notes
- Tests use extensive mocking for isolation.
- Focus on exception handling and edge cases.
- Static methods are mocked using PowerMock.

### Example Test Data
```json
{
  "appKey": "com.sankuai.test",
  "interfaceName": "com.test.Service",
  "methodName": "testMethod",
  "timeout": 5000
}
```