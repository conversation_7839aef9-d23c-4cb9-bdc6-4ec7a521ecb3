<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>algoplat-predictor</artifactId>
        <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <name>predictor-client</name>
    <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
    <artifactId>predictor-client</artifactId>
    <version>1.0.3</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.sankuai.dolphin</groupId>
                <artifactId>thrift-maven-plugin</artifactId>
                <version>1.2.5</version>
                <configuration>
                    <appkey>com.sankuai.algoplatform.predictor</appkey>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>