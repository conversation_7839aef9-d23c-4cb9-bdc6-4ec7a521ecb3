/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.sankuai.meishi.stgy.algoplatform.predictor.client;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @TypeDoc(
 *     description = "预测请求"
 * )
 */
public class TPredictRequest implements org.apache.thrift.TBase<TPredictRequest, TPredictRequest._Fields>, java.io.Serializable, Cloneable {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TPredictRequest");

  private static final org.apache.thrift.protocol.TField BIZ_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("bizCode", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField ABTEST_KEY_FIELD_DESC = new org.apache.thrift.protocol.TField("abtestKey", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField REQ_FIELD_DESC = new org.apache.thrift.protocol.TField("req", org.apache.thrift.protocol.TType.MAP, (short)3);
  private static final org.apache.thrift.protocol.TField EXTRA_FIELD_DESC = new org.apache.thrift.protocol.TField("extra", org.apache.thrift.protocol.TType.MAP, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TPredictRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TPredictRequestTupleSchemeFactory());
  }

  /**
   * @FieldDoc(
   *     description = "业务Code",
   *     example = {}
   * )
   */
  public String bizCode; // required
  /**
   * @FieldDoc(
   *     description = "用于ABTest的Key",
   *     example = {}
   * )
   */
  public String abtestKey; // optional
  /**
   * @FieldDoc(
   *     description = "算法预测请求",
   *     example = {}
   * )
   */
  public Map<String,String> req; // optional
  /**
   * @FieldDoc(
   *     description = "额外信息",
   *     example = {}
   * )
   */
  public Map<String,String> extra; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * @FieldDoc(
     *     description = "业务Code",
     *     example = {}
     * )
     */
    BIZ_CODE((short)1, "bizCode"),
    /**
     * @FieldDoc(
     *     description = "用于ABTest的Key",
     *     example = {}
     * )
     */
    ABTEST_KEY((short)2, "abtestKey"),
    /**
     * @FieldDoc(
     *     description = "算法预测请求",
     *     example = {}
     * )
     */
    REQ((short)3, "req"),
    /**
     * @FieldDoc(
     *     description = "额外信息",
     *     example = {}
     * )
     */
    EXTRA((short)4, "extra");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BIZ_CODE
          return BIZ_CODE;
        case 2: // ABTEST_KEY
          return ABTEST_KEY;
        case 3: // REQ
          return REQ;
        case 4: // EXTRA
          return EXTRA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private _Fields optionals[] = {_Fields.ABTEST_KEY,_Fields.REQ,_Fields.EXTRA};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BIZ_CODE, new org.apache.thrift.meta_data.FieldMetaData("bizCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ABTEST_KEY, new org.apache.thrift.meta_data.FieldMetaData("abtestKey", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REQ, new org.apache.thrift.meta_data.FieldMetaData("req", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.EXTRA, new org.apache.thrift.meta_data.FieldMetaData("extra", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TPredictRequest.class, metaDataMap);
  }

  public TPredictRequest() {
  }

  public TPredictRequest(
    String bizCode)
  {
    this();
    this.bizCode = bizCode;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TPredictRequest(TPredictRequest other) {
    if (other.isSetBizCode()) {
      this.bizCode = other.bizCode;
    }
    if (other.isSetAbtestKey()) {
      this.abtestKey = other.abtestKey;
    }
    if (other.isSetReq()) {
      Map<String,String> __this__req = new HashMap<String,String>();
      for (Map.Entry<String, String> other_element : other.req.entrySet()) {

        String other_element_key = other_element.getKey();
        String other_element_value = other_element.getValue();

        String __this__req_copy_key = other_element_key;

        String __this__req_copy_value = other_element_value;

        __this__req.put(__this__req_copy_key, __this__req_copy_value);
      }
      this.req = __this__req;
    }
    if (other.isSetExtra()) {
      Map<String,String> __this__extra = new HashMap<String,String>();
      for (Map.Entry<String, String> other_element : other.extra.entrySet()) {

        String other_element_key = other_element.getKey();
        String other_element_value = other_element.getValue();

        String __this__extra_copy_key = other_element_key;

        String __this__extra_copy_value = other_element_value;

        __this__extra.put(__this__extra_copy_key, __this__extra_copy_value);
      }
      this.extra = __this__extra;
    }
  }

  public TPredictRequest deepCopy() {
    return new TPredictRequest(this);
  }

  @Override
  public void clear() {
    this.bizCode = null;
    this.abtestKey = null;
    this.req = null;
    this.extra = null;
  }

  /**
   * @FieldDoc(
   *     description = "业务Code",
   *     example = {}
   * )
   */
  public String getBizCode() {
    return this.bizCode;
  }

  /**
   * @FieldDoc(
   *     description = "业务Code",
   *     example = {}
   * )
   */
  public TPredictRequest setBizCode(String bizCode) {
    this.bizCode = bizCode;
    return this;
  }

  public void unsetBizCode() {
    this.bizCode = null;
  }

  /** Returns true if field bizCode is set (has been assigned a value) and false otherwise */
  public boolean isSetBizCode() {
    return this.bizCode != null;
  }

  public void setBizCodeIsSet(boolean value) {
    if (!value) {
      this.bizCode = null;
    }
  }

  /**
   * @FieldDoc(
   *     description = "用于ABTest的Key",
   *     example = {}
   * )
   */
  public String getAbtestKey() {
    return this.abtestKey;
  }

  /**
   * @FieldDoc(
   *     description = "用于ABTest的Key",
   *     example = {}
   * )
   */
  public TPredictRequest setAbtestKey(String abtestKey) {
    this.abtestKey = abtestKey;
    return this;
  }

  public void unsetAbtestKey() {
    this.abtestKey = null;
  }

  /** Returns true if field abtestKey is set (has been assigned a value) and false otherwise */
  public boolean isSetAbtestKey() {
    return this.abtestKey != null;
  }

  public void setAbtestKeyIsSet(boolean value) {
    if (!value) {
      this.abtestKey = null;
    }
  }

  public int getReqSize() {
    return (this.req == null) ? 0 : this.req.size();
  }

  public void putToReq(String key, String val) {
    if (this.req == null) {
      this.req = new HashMap<String,String>();
    }
    this.req.put(key, val);
  }

  /**
   * @FieldDoc(
   *     description = "算法预测请求",
   *     example = {}
   * )
   */
  public Map<String,String> getReq() {
    return this.req;
  }

  /**
   * @FieldDoc(
   *     description = "算法预测请求",
   *     example = {}
   * )
   */
  public TPredictRequest setReq(Map<String,String> req) {
    this.req = req;
    return this;
  }

  public void unsetReq() {
    this.req = null;
  }

  /** Returns true if field req is set (has been assigned a value) and false otherwise */
  public boolean isSetReq() {
    return this.req != null;
  }

  public void setReqIsSet(boolean value) {
    if (!value) {
      this.req = null;
    }
  }

  public int getExtraSize() {
    return (this.extra == null) ? 0 : this.extra.size();
  }

  public void putToExtra(String key, String val) {
    if (this.extra == null) {
      this.extra = new HashMap<String,String>();
    }
    this.extra.put(key, val);
  }

  /**
   * @FieldDoc(
   *     description = "额外信息",
   *     example = {}
   * )
   */
  public Map<String,String> getExtra() {
    return this.extra;
  }

  /**
   * @FieldDoc(
   *     description = "额外信息",
   *     example = {}
   * )
   */
  public TPredictRequest setExtra(Map<String,String> extra) {
    this.extra = extra;
    return this;
  }

  public void unsetExtra() {
    this.extra = null;
  }

  /** Returns true if field extra is set (has been assigned a value) and false otherwise */
  public boolean isSetExtra() {
    return this.extra != null;
  }

  public void setExtraIsSet(boolean value) {
    if (!value) {
      this.extra = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case BIZ_CODE:
      if (value == null) {
        unsetBizCode();
      } else {
        setBizCode((String)value);
      }
      break;

    case ABTEST_KEY:
      if (value == null) {
        unsetAbtestKey();
      } else {
        setAbtestKey((String)value);
      }
      break;

    case REQ:
      if (value == null) {
        unsetReq();
      } else {
        setReq((Map<String,String>)value);
      }
      break;

    case EXTRA:
      if (value == null) {
        unsetExtra();
      } else {
        setExtra((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case BIZ_CODE:
      return getBizCode();

    case ABTEST_KEY:
      return getAbtestKey();

    case REQ:
      return getReq();

    case EXTRA:
      return getExtra();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case BIZ_CODE:
      return isSetBizCode();
    case ABTEST_KEY:
      return isSetAbtestKey();
    case REQ:
      return isSetReq();
    case EXTRA:
      return isSetExtra();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TPredictRequest)
      return this.equals((TPredictRequest)that);
    return false;
  }

  public boolean equals(TPredictRequest that) {
    if (that == null)
      return false;

    boolean this_present_bizCode = true && this.isSetBizCode();
    boolean that_present_bizCode = true && that.isSetBizCode();
    if (this_present_bizCode || that_present_bizCode) {
      if (!(this_present_bizCode && that_present_bizCode))
        return false;
      if (!this.bizCode.equals(that.bizCode))
        return false;
    }

    boolean this_present_abtestKey = true && this.isSetAbtestKey();
    boolean that_present_abtestKey = true && that.isSetAbtestKey();
    if (this_present_abtestKey || that_present_abtestKey) {
      if (!(this_present_abtestKey && that_present_abtestKey))
        return false;
      if (!this.abtestKey.equals(that.abtestKey))
        return false;
    }

    boolean this_present_req = true && this.isSetReq();
    boolean that_present_req = true && that.isSetReq();
    if (this_present_req || that_present_req) {
      if (!(this_present_req && that_present_req))
        return false;
      if (!this.req.equals(that.req))
        return false;
    }

    boolean this_present_extra = true && this.isSetExtra();
    boolean that_present_extra = true && that.isSetExtra();
    if (this_present_extra || that_present_extra) {
      if (!(this_present_extra && that_present_extra))
        return false;
      if (!this.extra.equals(that.extra))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    return 0;
  }

  public int compareTo(TPredictRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;
    TPredictRequest typedOther = (TPredictRequest)other;

    lastComparison = Boolean.valueOf(isSetBizCode()).compareTo(typedOther.isSetBizCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBizCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bizCode, typedOther.bizCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAbtestKey()).compareTo(typedOther.isSetAbtestKey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAbtestKey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.abtestKey, typedOther.abtestKey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReq()).compareTo(typedOther.isSetReq());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReq()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.req, typedOther.req);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtra()).compareTo(typedOther.isSetExtra());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtra()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extra, typedOther.extra);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TPredictRequest(");
    boolean first = true;

    sb.append("bizCode:");
    if (this.bizCode == null) {
      sb.append("null");
    } else {
      sb.append(this.bizCode);
    }
    first = false;
    if (isSetAbtestKey()) {
      if (!first) sb.append(", ");
      sb.append("abtestKey:");
      if (this.abtestKey == null) {
        sb.append("null");
      } else {
        sb.append(this.abtestKey);
      }
      first = false;
    }
    if (isSetReq()) {
      if (!first) sb.append(", ");
      sb.append("req:");
      if (this.req == null) {
        sb.append("null");
      } else {
        sb.append(this.req);
      }
      first = false;
    }
    if (isSetExtra()) {
      if (!first) sb.append(", ");
      sb.append("extra:");
      if (this.extra == null) {
        sb.append("null");
      } else {
        sb.append(this.extra);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (bizCode == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'bizCode' was not present! Struct: " + toString());
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TPredictRequestStandardSchemeFactory implements SchemeFactory {
    public TPredictRequestStandardScheme getScheme() {
      return new TPredictRequestStandardScheme();
    }
  }

  private static class TPredictRequestStandardScheme extends StandardScheme<TPredictRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TPredictRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BIZ_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.bizCode = iprot.readString();
              struct.setBizCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ABTEST_KEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.abtestKey = iprot.readString();
              struct.setAbtestKeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REQ
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map0 = iprot.readMapBegin();
                struct.req = new HashMap<String,String>(2*_map0.size);
                for (int _i1 = 0; _i1 < _map0.size; ++_i1)
                {
                  String _key2; // required
                  String _val3; // required
                  _key2 = iprot.readString();
                  _val3 = iprot.readString();
                  struct.req.put(_key2, _val3);
                }
                iprot.readMapEnd();
              }
              struct.setReqIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // EXTRA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map4 = iprot.readMapBegin();
                struct.extra = new HashMap<String,String>(2*_map4.size);
                for (int _i5 = 0; _i5 < _map4.size; ++_i5)
                {
                  String _key6; // required
                  String _val7; // required
                  _key6 = iprot.readString();
                  _val7 = iprot.readString();
                  struct.extra.put(_key6, _val7);
                }
                iprot.readMapEnd();
              }
              struct.setExtraIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TPredictRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.bizCode != null) {
        oprot.writeFieldBegin(BIZ_CODE_FIELD_DESC);
        oprot.writeString(struct.bizCode);
        oprot.writeFieldEnd();
      }
      if (struct.abtestKey != null) {
        if (struct.isSetAbtestKey()) {
          oprot.writeFieldBegin(ABTEST_KEY_FIELD_DESC);
          oprot.writeString(struct.abtestKey);
          oprot.writeFieldEnd();
        }
      }
      if (struct.req != null) {
        if (struct.isSetReq()) {
          oprot.writeFieldBegin(REQ_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.req.size()));
            for (Map.Entry<String, String> _iter8 : struct.req.entrySet())
            {
              oprot.writeString(_iter8.getKey());
              oprot.writeString(_iter8.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.extra != null) {
        if (struct.isSetExtra()) {
          oprot.writeFieldBegin(EXTRA_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extra.size()));
            for (Map.Entry<String, String> _iter9 : struct.extra.entrySet())
            {
              oprot.writeString(_iter9.getKey());
              oprot.writeString(_iter9.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TPredictRequestTupleSchemeFactory implements SchemeFactory {
    public TPredictRequestTupleScheme getScheme() {
      return new TPredictRequestTupleScheme();
    }
  }

  private static class TPredictRequestTupleScheme extends TupleScheme<TPredictRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TPredictRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeString(struct.bizCode);
      BitSet optionals = new BitSet();
      if (struct.isSetAbtestKey()) {
        optionals.set(0);
      }
      if (struct.isSetReq()) {
        optionals.set(1);
      }
      if (struct.isSetExtra()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetAbtestKey()) {
        oprot.writeString(struct.abtestKey);
      }
      if (struct.isSetReq()) {
        {
          oprot.writeI32(struct.req.size());
          for (Map.Entry<String, String> _iter10 : struct.req.entrySet())
          {
            oprot.writeString(_iter10.getKey());
            oprot.writeString(_iter10.getValue());
          }
        }
      }
      if (struct.isSetExtra()) {
        {
          oprot.writeI32(struct.extra.size());
          for (Map.Entry<String, String> _iter11 : struct.extra.entrySet())
          {
            oprot.writeString(_iter11.getKey());
            oprot.writeString(_iter11.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TPredictRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.bizCode = iprot.readString();
      struct.setBizCodeIsSet(true);
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.abtestKey = iprot.readString();
        struct.setAbtestKeyIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map12 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.req = new HashMap<String,String>(2*_map12.size);
          for (int _i13 = 0; _i13 < _map12.size; ++_i13)
          {
            String _key14; // required
            String _val15; // required
            _key14 = iprot.readString();
            _val15 = iprot.readString();
            struct.req.put(_key14, _val15);
          }
        }
        struct.setReqIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map16 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extra = new HashMap<String,String>(2*_map16.size);
          for (int _i17 = 0; _i17 < _map16.size; ++_i17)
          {
            String _key18; // required
            String _val19; // required
            _key18 = iprot.readString();
            _val19 = iprot.readString();
            struct.extra.put(_key18, _val19);
          }
        }
        struct.setExtraIsSet(true);
      }
    }
  }

}

