/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.sankuai.meishi.stgy.algoplatform.predictor.client;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @TypeDoc(
 *     description = "请求"
 * )
 */
public class TInvokeDirectlyRequest implements org.apache.thrift.TBase<TInvokeDirectlyRequest, TInvokeDirectlyRequest._Fields>, java.io.Serializable, Cloneable {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TInvokeDirectlyRequest");

  private static final org.apache.thrift.protocol.TField INTERPRETER_FIELD_DESC = new org.apache.thrift.protocol.TField("interpreter", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField PATH_FIELD_DESC = new org.apache.thrift.protocol.TField("path", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField METHOD_FIELD_DESC = new org.apache.thrift.protocol.TField("method", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("data", org.apache.thrift.protocol.TType.STRING, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TInvokeDirectlyRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TInvokeDirectlyRequestTupleSchemeFactory());
  }

  /**
   * @FieldDoc(
   *     description = "解释器",
   *     example = {}
   * )
   */
  public String interpreter; // required
  /**
   * @FieldDoc(
   *     description = "算法包路径",
   *     example = {}
   * )
   */
  public String path; // required
  /**
   * @FieldDoc(
   *     description = "调用方法",
   *     example = {}
   * )
   */
  public String method; // required
  /**
   * @FieldDoc(
   *     description = "入参数据",
   *     example = {}
   * )
   */
  public String data; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * @FieldDoc(
     *     description = "解释器",
     *     example = {}
     * )
     */
    INTERPRETER((short)1, "interpreter"),
    /**
     * @FieldDoc(
     *     description = "算法包路径",
     *     example = {}
     * )
     */
    PATH((short)2, "path"),
    /**
     * @FieldDoc(
     *     description = "调用方法",
     *     example = {}
     * )
     */
    METHOD((short)3, "method"),
    /**
     * @FieldDoc(
     *     description = "入参数据",
     *     example = {}
     * )
     */
    DATA((short)4, "data");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // INTERPRETER
          return INTERPRETER;
        case 2: // PATH
          return PATH;
        case 3: // METHOD
          return METHOD;
        case 4: // DATA
          return DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.INTERPRETER, new org.apache.thrift.meta_data.FieldMetaData("interpreter", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PATH, new org.apache.thrift.meta_data.FieldMetaData("path", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.METHOD, new org.apache.thrift.meta_data.FieldMetaData("method", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.DATA, new org.apache.thrift.meta_data.FieldMetaData("data", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TInvokeDirectlyRequest.class, metaDataMap);
  }

  public TInvokeDirectlyRequest() {
  }

  public TInvokeDirectlyRequest(
    String interpreter,
    String path,
    String method,
    String data)
  {
    this();
    this.interpreter = interpreter;
    this.path = path;
    this.method = method;
    this.data = data;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TInvokeDirectlyRequest(TInvokeDirectlyRequest other) {
    if (other.isSetInterpreter()) {
      this.interpreter = other.interpreter;
    }
    if (other.isSetPath()) {
      this.path = other.path;
    }
    if (other.isSetMethod()) {
      this.method = other.method;
    }
    if (other.isSetData()) {
      this.data = other.data;
    }
  }

  public TInvokeDirectlyRequest deepCopy() {
    return new TInvokeDirectlyRequest(this);
  }

  @Override
  public void clear() {
    this.interpreter = null;
    this.path = null;
    this.method = null;
    this.data = null;
  }

  /**
   * @FieldDoc(
   *     description = "解释器",
   *     example = {}
   * )
   */
  public String getInterpreter() {
    return this.interpreter;
  }

  /**
   * @FieldDoc(
   *     description = "解释器",
   *     example = {}
   * )
   */
  public TInvokeDirectlyRequest setInterpreter(String interpreter) {
    this.interpreter = interpreter;
    return this;
  }

  public void unsetInterpreter() {
    this.interpreter = null;
  }

  /** Returns true if field interpreter is set (has been assigned a value) and false otherwise */
  public boolean isSetInterpreter() {
    return this.interpreter != null;
  }

  public void setInterpreterIsSet(boolean value) {
    if (!value) {
      this.interpreter = null;
    }
  }

  /**
   * @FieldDoc(
   *     description = "算法包路径",
   *     example = {}
   * )
   */
  public String getPath() {
    return this.path;
  }

  /**
   * @FieldDoc(
   *     description = "算法包路径",
   *     example = {}
   * )
   */
  public TInvokeDirectlyRequest setPath(String path) {
    this.path = path;
    return this;
  }

  public void unsetPath() {
    this.path = null;
  }

  /** Returns true if field path is set (has been assigned a value) and false otherwise */
  public boolean isSetPath() {
    return this.path != null;
  }

  public void setPathIsSet(boolean value) {
    if (!value) {
      this.path = null;
    }
  }

  /**
   * @FieldDoc(
   *     description = "调用方法",
   *     example = {}
   * )
   */
  public String getMethod() {
    return this.method;
  }

  /**
   * @FieldDoc(
   *     description = "调用方法",
   *     example = {}
   * )
   */
  public TInvokeDirectlyRequest setMethod(String method) {
    this.method = method;
    return this;
  }

  public void unsetMethod() {
    this.method = null;
  }

  /** Returns true if field method is set (has been assigned a value) and false otherwise */
  public boolean isSetMethod() {
    return this.method != null;
  }

  public void setMethodIsSet(boolean value) {
    if (!value) {
      this.method = null;
    }
  }

  /**
   * @FieldDoc(
   *     description = "入参数据",
   *     example = {}
   * )
   */
  public String getData() {
    return this.data;
  }

  /**
   * @FieldDoc(
   *     description = "入参数据",
   *     example = {}
   * )
   */
  public TInvokeDirectlyRequest setData(String data) {
    this.data = data;
    return this;
  }

  public void unsetData() {
    this.data = null;
  }

  /** Returns true if field data is set (has been assigned a value) and false otherwise */
  public boolean isSetData() {
    return this.data != null;
  }

  public void setDataIsSet(boolean value) {
    if (!value) {
      this.data = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case INTERPRETER:
      if (value == null) {
        unsetInterpreter();
      } else {
        setInterpreter((String)value);
      }
      break;

    case PATH:
      if (value == null) {
        unsetPath();
      } else {
        setPath((String)value);
      }
      break;

    case METHOD:
      if (value == null) {
        unsetMethod();
      } else {
        setMethod((String)value);
      }
      break;

    case DATA:
      if (value == null) {
        unsetData();
      } else {
        setData((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case INTERPRETER:
      return getInterpreter();

    case PATH:
      return getPath();

    case METHOD:
      return getMethod();

    case DATA:
      return getData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case INTERPRETER:
      return isSetInterpreter();
    case PATH:
      return isSetPath();
    case METHOD:
      return isSetMethod();
    case DATA:
      return isSetData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TInvokeDirectlyRequest)
      return this.equals((TInvokeDirectlyRequest)that);
    return false;
  }

  public boolean equals(TInvokeDirectlyRequest that) {
    if (that == null)
      return false;

    boolean this_present_interpreter = true && this.isSetInterpreter();
    boolean that_present_interpreter = true && that.isSetInterpreter();
    if (this_present_interpreter || that_present_interpreter) {
      if (!(this_present_interpreter && that_present_interpreter))
        return false;
      if (!this.interpreter.equals(that.interpreter))
        return false;
    }

    boolean this_present_path = true && this.isSetPath();
    boolean that_present_path = true && that.isSetPath();
    if (this_present_path || that_present_path) {
      if (!(this_present_path && that_present_path))
        return false;
      if (!this.path.equals(that.path))
        return false;
    }

    boolean this_present_method = true && this.isSetMethod();
    boolean that_present_method = true && that.isSetMethod();
    if (this_present_method || that_present_method) {
      if (!(this_present_method && that_present_method))
        return false;
      if (!this.method.equals(that.method))
        return false;
    }

    boolean this_present_data = true && this.isSetData();
    boolean that_present_data = true && that.isSetData();
    if (this_present_data || that_present_data) {
      if (!(this_present_data && that_present_data))
        return false;
      if (!this.data.equals(that.data))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    return 0;
  }

  public int compareTo(TInvokeDirectlyRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;
    TInvokeDirectlyRequest typedOther = (TInvokeDirectlyRequest)other;

    lastComparison = Boolean.valueOf(isSetInterpreter()).compareTo(typedOther.isSetInterpreter());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetInterpreter()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.interpreter, typedOther.interpreter);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPath()).compareTo(typedOther.isSetPath());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPath()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.path, typedOther.path);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMethod()).compareTo(typedOther.isSetMethod());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMethod()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.method, typedOther.method);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetData()).compareTo(typedOther.isSetData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.data, typedOther.data);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TInvokeDirectlyRequest(");
    boolean first = true;

    sb.append("interpreter:");
    if (this.interpreter == null) {
      sb.append("null");
    } else {
      sb.append(this.interpreter);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("path:");
    if (this.path == null) {
      sb.append("null");
    } else {
      sb.append(this.path);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("method:");
    if (this.method == null) {
      sb.append("null");
    } else {
      sb.append(this.method);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("data:");
    if (this.data == null) {
      sb.append("null");
    } else {
      sb.append(this.data);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (interpreter == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'interpreter' was not present! Struct: " + toString());
    }
    if (path == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'path' was not present! Struct: " + toString());
    }
    if (method == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'method' was not present! Struct: " + toString());
    }
    if (data == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'data' was not present! Struct: " + toString());
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TInvokeDirectlyRequestStandardSchemeFactory implements SchemeFactory {
    public TInvokeDirectlyRequestStandardScheme getScheme() {
      return new TInvokeDirectlyRequestStandardScheme();
    }
  }

  private static class TInvokeDirectlyRequestStandardScheme extends StandardScheme<TInvokeDirectlyRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TInvokeDirectlyRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // INTERPRETER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.interpreter = iprot.readString();
              struct.setInterpreterIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // PATH
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.path = iprot.readString();
              struct.setPathIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // METHOD
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.method = iprot.readString();
              struct.setMethodIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.data = iprot.readString();
              struct.setDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TInvokeDirectlyRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.interpreter != null) {
        oprot.writeFieldBegin(INTERPRETER_FIELD_DESC);
        oprot.writeString(struct.interpreter);
        oprot.writeFieldEnd();
      }
      if (struct.path != null) {
        oprot.writeFieldBegin(PATH_FIELD_DESC);
        oprot.writeString(struct.path);
        oprot.writeFieldEnd();
      }
      if (struct.method != null) {
        oprot.writeFieldBegin(METHOD_FIELD_DESC);
        oprot.writeString(struct.method);
        oprot.writeFieldEnd();
      }
      if (struct.data != null) {
        oprot.writeFieldBegin(DATA_FIELD_DESC);
        oprot.writeString(struct.data);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TInvokeDirectlyRequestTupleSchemeFactory implements SchemeFactory {
    public TInvokeDirectlyRequestTupleScheme getScheme() {
      return new TInvokeDirectlyRequestTupleScheme();
    }
  }

  private static class TInvokeDirectlyRequestTupleScheme extends TupleScheme<TInvokeDirectlyRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TInvokeDirectlyRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeString(struct.interpreter);
      oprot.writeString(struct.path);
      oprot.writeString(struct.method);
      oprot.writeString(struct.data);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TInvokeDirectlyRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.interpreter = iprot.readString();
      struct.setInterpreterIsSet(true);
      struct.path = iprot.readString();
      struct.setPathIsSet(true);
      struct.method = iprot.readString();
      struct.setMethodIsSet(true);
      struct.data = iprot.readString();
      struct.setDataIsSet(true);
    }
  }

}

