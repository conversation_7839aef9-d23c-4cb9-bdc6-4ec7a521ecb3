/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.sankuai.meishi.stgy.algoplatform.predictor.client;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @TypeDoc(
 *     description = "返回"
 * )
 */
public class TScriptResponse implements org.apache.thrift.TBase<TScriptResponse, TScriptResponse._Fields>, java.io.Serializable, Cloneable {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TScriptResponse");

  private static final org.apache.thrift.protocol.TField CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("code", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField SCRIPT_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("scriptMap", org.apache.thrift.protocol.TType.MAP, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TScriptResponseStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TScriptResponseTupleSchemeFactory());
  }

  /**
   * @FieldDoc(
   *     description = "正常为0，非0为异常",
   *     example = {}
   * )
   */
  public int code; // required
  /**
   * @FieldDoc(
   *     description = "返回数据",
   *     example = {}
   * )
   */
  public Map<String,String> scriptMap; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * @FieldDoc(
     *     description = "正常为0，非0为异常",
     *     example = {}
     * )
     */
    CODE((short)1, "code"),
    /**
     * @FieldDoc(
     *     description = "返回数据",
     *     example = {}
     * )
     */
    SCRIPT_MAP((short)2, "scriptMap");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CODE
          return CODE;
        case 2: // SCRIPT_MAP
          return SCRIPT_MAP;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __CODE_ISSET_ID = 0;
  private BitSet __isset_bit_vector = new BitSet(1);
  private _Fields optionals[] = {_Fields.SCRIPT_MAP};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CODE, new org.apache.thrift.meta_data.FieldMetaData("code", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SCRIPT_MAP, new org.apache.thrift.meta_data.FieldMetaData("scriptMap", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TScriptResponse.class, metaDataMap);
  }

  public TScriptResponse() {
  }

  public TScriptResponse(
    int code)
  {
    this();
    this.code = code;
    setCodeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TScriptResponse(TScriptResponse other) {
    __isset_bit_vector.clear();
    __isset_bit_vector.or(other.__isset_bit_vector);
    this.code = other.code;
    if (other.isSetScriptMap()) {
      Map<String,String> __this__scriptMap = new HashMap<String,String>();
      for (Map.Entry<String, String> other_element : other.scriptMap.entrySet()) {

        String other_element_key = other_element.getKey();
        String other_element_value = other_element.getValue();

        String __this__scriptMap_copy_key = other_element_key;

        String __this__scriptMap_copy_value = other_element_value;

        __this__scriptMap.put(__this__scriptMap_copy_key, __this__scriptMap_copy_value);
      }
      this.scriptMap = __this__scriptMap;
    }
  }

  public TScriptResponse deepCopy() {
    return new TScriptResponse(this);
  }

  @Override
  public void clear() {
    setCodeIsSet(false);
    this.code = 0;
    this.scriptMap = null;
  }

  /**
   * @FieldDoc(
   *     description = "正常为0，非0为异常",
   *     example = {}
   * )
   */
  public int getCode() {
    return this.code;
  }

  /**
   * @FieldDoc(
   *     description = "正常为0，非0为异常",
   *     example = {}
   * )
   */
  public TScriptResponse setCode(int code) {
    this.code = code;
    setCodeIsSet(true);
    return this;
  }

  public void unsetCode() {
    __isset_bit_vector.clear(__CODE_ISSET_ID);
  }

  /** Returns true if field code is set (has been assigned a value) and false otherwise */
  public boolean isSetCode() {
    return __isset_bit_vector.get(__CODE_ISSET_ID);
  }

  public void setCodeIsSet(boolean value) {
    __isset_bit_vector.set(__CODE_ISSET_ID, value);
  }

  public int getScriptMapSize() {
    return (this.scriptMap == null) ? 0 : this.scriptMap.size();
  }

  public void putToScriptMap(String key, String val) {
    if (this.scriptMap == null) {
      this.scriptMap = new HashMap<String,String>();
    }
    this.scriptMap.put(key, val);
  }

  /**
   * @FieldDoc(
   *     description = "返回数据",
   *     example = {}
   * )
   */
  public Map<String,String> getScriptMap() {
    return this.scriptMap;
  }

  /**
   * @FieldDoc(
   *     description = "返回数据",
   *     example = {}
   * )
   */
  public TScriptResponse setScriptMap(Map<String,String> scriptMap) {
    this.scriptMap = scriptMap;
    return this;
  }

  public void unsetScriptMap() {
    this.scriptMap = null;
  }

  /** Returns true if field scriptMap is set (has been assigned a value) and false otherwise */
  public boolean isSetScriptMap() {
    return this.scriptMap != null;
  }

  public void setScriptMapIsSet(boolean value) {
    if (!value) {
      this.scriptMap = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case CODE:
      if (value == null) {
        unsetCode();
      } else {
        setCode((Integer)value);
      }
      break;

    case SCRIPT_MAP:
      if (value == null) {
        unsetScriptMap();
      } else {
        setScriptMap((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case CODE:
      return Integer.valueOf(getCode());

    case SCRIPT_MAP:
      return getScriptMap();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case CODE:
      return isSetCode();
    case SCRIPT_MAP:
      return isSetScriptMap();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TScriptResponse)
      return this.equals((TScriptResponse)that);
    return false;
  }

  public boolean equals(TScriptResponse that) {
    if (that == null)
      return false;

    boolean this_present_code = true;
    boolean that_present_code = true;
    if (this_present_code || that_present_code) {
      if (!(this_present_code && that_present_code))
        return false;
      if (this.code != that.code)
        return false;
    }

    boolean this_present_scriptMap = true && this.isSetScriptMap();
    boolean that_present_scriptMap = true && that.isSetScriptMap();
    if (this_present_scriptMap || that_present_scriptMap) {
      if (!(this_present_scriptMap && that_present_scriptMap))
        return false;
      if (!this.scriptMap.equals(that.scriptMap))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    return 0;
  }

  public int compareTo(TScriptResponse other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;
    TScriptResponse typedOther = (TScriptResponse)other;

    lastComparison = Boolean.valueOf(isSetCode()).compareTo(typedOther.isSetCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.code, typedOther.code);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetScriptMap()).compareTo(typedOther.isSetScriptMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetScriptMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.scriptMap, typedOther.scriptMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TScriptResponse(");
    boolean first = true;

    sb.append("code:");
    sb.append(this.code);
    first = false;
    if (isSetScriptMap()) {
      if (!first) sb.append(", ");
      sb.append("scriptMap:");
      if (this.scriptMap == null) {
        sb.append("null");
      } else {
        sb.append(this.scriptMap);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'code' because it's a primitive and you chose the non-beans generator.
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bit_vector = new BitSet(1);
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TScriptResponseStandardSchemeFactory implements SchemeFactory {
    public TScriptResponseStandardScheme getScheme() {
      return new TScriptResponseStandardScheme();
    }
  }

  private static class TScriptResponseStandardScheme extends StandardScheme<TScriptResponse> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TScriptResponse struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.code = iprot.readI32();
              struct.setCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SCRIPT_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map104 = iprot.readMapBegin();
                struct.scriptMap = new HashMap<String,String>(2*_map104.size);
                for (int _i105 = 0; _i105 < _map104.size; ++_i105)
                {
                  String _key106; // required
                  String _val107; // required
                  _key106 = iprot.readString();
                  _val107 = iprot.readString();
                  struct.scriptMap.put(_key106, _val107);
                }
                iprot.readMapEnd();
              }
              struct.setScriptMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetCode()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'code' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TScriptResponse struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(CODE_FIELD_DESC);
      oprot.writeI32(struct.code);
      oprot.writeFieldEnd();
      if (struct.scriptMap != null) {
        if (struct.isSetScriptMap()) {
          oprot.writeFieldBegin(SCRIPT_MAP_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.scriptMap.size()));
            for (Map.Entry<String, String> _iter108 : struct.scriptMap.entrySet())
            {
              oprot.writeString(_iter108.getKey());
              oprot.writeString(_iter108.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TScriptResponseTupleSchemeFactory implements SchemeFactory {
    public TScriptResponseTupleScheme getScheme() {
      return new TScriptResponseTupleScheme();
    }
  }

  private static class TScriptResponseTupleScheme extends TupleScheme<TScriptResponse> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TScriptResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI32(struct.code);
      BitSet optionals = new BitSet();
      if (struct.isSetScriptMap()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetScriptMap()) {
        {
          oprot.writeI32(struct.scriptMap.size());
          for (Map.Entry<String, String> _iter109 : struct.scriptMap.entrySet())
          {
            oprot.writeString(_iter109.getKey());
            oprot.writeString(_iter109.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TScriptResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.code = iprot.readI32();
      struct.setCodeIsSet(true);
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TMap _map110 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.scriptMap = new HashMap<String,String>(2*_map110.size);
          for (int _i111 = 0; _i111 < _map110.size; ++_i111)
          {
            String _key112; // required
            String _val113; // required
            _key112 = iprot.readString();
            _val113 = iprot.readString();
            struct.scriptMap.put(_key112, _val113);
          }
        }
        struct.setScriptMapIsSet(true);
      }
    }
  }

}

