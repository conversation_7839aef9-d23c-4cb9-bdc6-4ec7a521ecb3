/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.sankuai.meishi.stgy.algoplatform.predictor.client;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @TypeDoc(
 *     description = "返回"
 * )
 */
public class TInvokeDirectlyResponse implements org.apache.thrift.TBase<TInvokeDirectlyResponse, TInvokeDirectlyResponse._Fields>, java.io.Serializable, Cloneable {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TInvokeDirectlyResponse");

  private static final org.apache.thrift.protocol.TField COST_FIELD_DESC = new org.apache.thrift.protocol.TField("cost", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("data", org.apache.thrift.protocol.TType.STRING, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TInvokeDirectlyResponseStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TInvokeDirectlyResponseTupleSchemeFactory());
  }

  /**
   * @FieldDoc(
   *     description = "时间花费",
   *     example = {}
   * )
   */
  public long cost; // required
  /**
   * @FieldDoc(
   *     description = "返回数据",
   *     example = {}
   * )
   */
  public String data; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * @FieldDoc(
     *     description = "时间花费",
     *     example = {}
     * )
     */
    COST((short)1, "cost"),
    /**
     * @FieldDoc(
     *     description = "返回数据",
     *     example = {}
     * )
     */
    DATA((short)2, "data");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // COST
          return COST;
        case 2: // DATA
          return DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __COST_ISSET_ID = 0;
  private BitSet __isset_bit_vector = new BitSet(1);
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.COST, new org.apache.thrift.meta_data.FieldMetaData("cost", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DATA, new org.apache.thrift.meta_data.FieldMetaData("data", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TInvokeDirectlyResponse.class, metaDataMap);
  }

  public TInvokeDirectlyResponse() {
  }

  public TInvokeDirectlyResponse(
    long cost,
    String data)
  {
    this();
    this.cost = cost;
    setCostIsSet(true);
    this.data = data;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TInvokeDirectlyResponse(TInvokeDirectlyResponse other) {
    __isset_bit_vector.clear();
    __isset_bit_vector.or(other.__isset_bit_vector);
    this.cost = other.cost;
    if (other.isSetData()) {
      this.data = other.data;
    }
  }

  public TInvokeDirectlyResponse deepCopy() {
    return new TInvokeDirectlyResponse(this);
  }

  @Override
  public void clear() {
    setCostIsSet(false);
    this.cost = 0;
    this.data = null;
  }

  /**
   * @FieldDoc(
   *     description = "时间花费",
   *     example = {}
   * )
   */
  public long getCost() {
    return this.cost;
  }

  /**
   * @FieldDoc(
   *     description = "时间花费",
   *     example = {}
   * )
   */
  public TInvokeDirectlyResponse setCost(long cost) {
    this.cost = cost;
    setCostIsSet(true);
    return this;
  }

  public void unsetCost() {
    __isset_bit_vector.clear(__COST_ISSET_ID);
  }

  /** Returns true if field cost is set (has been assigned a value) and false otherwise */
  public boolean isSetCost() {
    return __isset_bit_vector.get(__COST_ISSET_ID);
  }

  public void setCostIsSet(boolean value) {
    __isset_bit_vector.set(__COST_ISSET_ID, value);
  }

  /**
   * @FieldDoc(
   *     description = "返回数据",
   *     example = {}
   * )
   */
  public String getData() {
    return this.data;
  }

  /**
   * @FieldDoc(
   *     description = "返回数据",
   *     example = {}
   * )
   */
  public TInvokeDirectlyResponse setData(String data) {
    this.data = data;
    return this;
  }

  public void unsetData() {
    this.data = null;
  }

  /** Returns true if field data is set (has been assigned a value) and false otherwise */
  public boolean isSetData() {
    return this.data != null;
  }

  public void setDataIsSet(boolean value) {
    if (!value) {
      this.data = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case COST:
      if (value == null) {
        unsetCost();
      } else {
        setCost((Long)value);
      }
      break;

    case DATA:
      if (value == null) {
        unsetData();
      } else {
        setData((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case COST:
      return Long.valueOf(getCost());

    case DATA:
      return getData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case COST:
      return isSetCost();
    case DATA:
      return isSetData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TInvokeDirectlyResponse)
      return this.equals((TInvokeDirectlyResponse)that);
    return false;
  }

  public boolean equals(TInvokeDirectlyResponse that) {
    if (that == null)
      return false;

    boolean this_present_cost = true;
    boolean that_present_cost = true;
    if (this_present_cost || that_present_cost) {
      if (!(this_present_cost && that_present_cost))
        return false;
      if (this.cost != that.cost)
        return false;
    }

    boolean this_present_data = true && this.isSetData();
    boolean that_present_data = true && that.isSetData();
    if (this_present_data || that_present_data) {
      if (!(this_present_data && that_present_data))
        return false;
      if (!this.data.equals(that.data))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    return 0;
  }

  public int compareTo(TInvokeDirectlyResponse other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;
    TInvokeDirectlyResponse typedOther = (TInvokeDirectlyResponse)other;

    lastComparison = Boolean.valueOf(isSetCost()).compareTo(typedOther.isSetCost());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCost()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cost, typedOther.cost);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetData()).compareTo(typedOther.isSetData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.data, typedOther.data);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TInvokeDirectlyResponse(");
    boolean first = true;

    sb.append("cost:");
    sb.append(this.cost);
    first = false;
    if (!first) sb.append(", ");
    sb.append("data:");
    if (this.data == null) {
      sb.append("null");
    } else {
      sb.append(this.data);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'cost' because it's a primitive and you chose the non-beans generator.
    if (data == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'data' was not present! Struct: " + toString());
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bit_vector = new BitSet(1);
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TInvokeDirectlyResponseStandardSchemeFactory implements SchemeFactory {
    public TInvokeDirectlyResponseStandardScheme getScheme() {
      return new TInvokeDirectlyResponseStandardScheme();
    }
  }

  private static class TInvokeDirectlyResponseStandardScheme extends StandardScheme<TInvokeDirectlyResponse> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TInvokeDirectlyResponse struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // COST
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.cost = iprot.readI64();
              struct.setCostIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.data = iprot.readString();
              struct.setDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetCost()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'cost' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TInvokeDirectlyResponse struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(COST_FIELD_DESC);
      oprot.writeI64(struct.cost);
      oprot.writeFieldEnd();
      if (struct.data != null) {
        oprot.writeFieldBegin(DATA_FIELD_DESC);
        oprot.writeString(struct.data);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TInvokeDirectlyResponseTupleSchemeFactory implements SchemeFactory {
    public TInvokeDirectlyResponseTupleScheme getScheme() {
      return new TInvokeDirectlyResponseTupleScheme();
    }
  }

  private static class TInvokeDirectlyResponseTupleScheme extends TupleScheme<TInvokeDirectlyResponse> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TInvokeDirectlyResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI64(struct.cost);
      oprot.writeString(struct.data);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TInvokeDirectlyResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.cost = iprot.readI64();
      struct.setCostIsSet(true);
      struct.data = iprot.readString();
      struct.setDataIsSet(true);
    }
  }

}

