/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.sankuai.meishi.stgy.algoplatform.predictor.client;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @TypeDoc(
 *     description = "请求"
 * )
 */
public class TPredictionRequest implements org.apache.thrift.TBase<TPredictionRequest, TPredictionRequest._Fields>, java.io.Serializable, Cloneable {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TPredictionRequest");

  private static final org.apache.thrift.protocol.TField BIZ_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("bizCode", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField ENTITY_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("entityIds", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField EXTRA_FIELD_DESC = new org.apache.thrift.protocol.TField("extra", org.apache.thrift.protocol.TType.MAP, (short)3);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TPredictionRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TPredictionRequestTupleSchemeFactory());
  }

  /**
   * @FieldDoc(
   *     description = "业务标识",
   *     example = {}
   * )
   */
  public String bizCode; // required
  /**
   * @FieldDoc(
   *     description = "查询实体ids",
   *     example = {}
   * )
   */
  public List<String> entityIds; // required
  /**
   * @FieldDoc(
   *     description = "额外信息",
   *     example = {}
   * )
   */
  public Map<String,String> extra; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * @FieldDoc(
     *     description = "业务标识",
     *     example = {}
     * )
     */
    BIZ_CODE((short)1, "bizCode"),
    /**
     * @FieldDoc(
     *     description = "查询实体ids",
     *     example = {}
     * )
     */
    ENTITY_IDS((short)2, "entityIds"),
    /**
     * @FieldDoc(
     *     description = "额外信息",
     *     example = {}
     * )
     */
    EXTRA((short)3, "extra");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BIZ_CODE
          return BIZ_CODE;
        case 2: // ENTITY_IDS
          return ENTITY_IDS;
        case 3: // EXTRA
          return EXTRA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private _Fields optionals[] = {_Fields.EXTRA};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BIZ_CODE, new org.apache.thrift.meta_data.FieldMetaData("bizCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ENTITY_IDS, new org.apache.thrift.meta_data.FieldMetaData("entityIds", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.EXTRA, new org.apache.thrift.meta_data.FieldMetaData("extra", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TPredictionRequest.class, metaDataMap);
  }

  public TPredictionRequest() {
  }

  public TPredictionRequest(
    String bizCode,
    List<String> entityIds)
  {
    this();
    this.bizCode = bizCode;
    this.entityIds = entityIds;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TPredictionRequest(TPredictionRequest other) {
    if (other.isSetBizCode()) {
      this.bizCode = other.bizCode;
    }
    if (other.isSetEntityIds()) {
      List<String> __this__entityIds = new ArrayList<String>();
      for (String other_element : other.entityIds) {
        __this__entityIds.add(other_element);
      }
      this.entityIds = __this__entityIds;
    }
    if (other.isSetExtra()) {
      Map<String,String> __this__extra = new HashMap<String,String>();
      for (Map.Entry<String, String> other_element : other.extra.entrySet()) {

        String other_element_key = other_element.getKey();
        String other_element_value = other_element.getValue();

        String __this__extra_copy_key = other_element_key;

        String __this__extra_copy_value = other_element_value;

        __this__extra.put(__this__extra_copy_key, __this__extra_copy_value);
      }
      this.extra = __this__extra;
    }
  }

  public TPredictionRequest deepCopy() {
    return new TPredictionRequest(this);
  }

  @Override
  public void clear() {
    this.bizCode = null;
    this.entityIds = null;
    this.extra = null;
  }

  /**
   * @FieldDoc(
   *     description = "业务标识",
   *     example = {}
   * )
   */
  public String getBizCode() {
    return this.bizCode;
  }

  /**
   * @FieldDoc(
   *     description = "业务标识",
   *     example = {}
   * )
   */
  public TPredictionRequest setBizCode(String bizCode) {
    this.bizCode = bizCode;
    return this;
  }

  public void unsetBizCode() {
    this.bizCode = null;
  }

  /** Returns true if field bizCode is set (has been assigned a value) and false otherwise */
  public boolean isSetBizCode() {
    return this.bizCode != null;
  }

  public void setBizCodeIsSet(boolean value) {
    if (!value) {
      this.bizCode = null;
    }
  }

  public int getEntityIdsSize() {
    return (this.entityIds == null) ? 0 : this.entityIds.size();
  }

  public java.util.Iterator<String> getEntityIdsIterator() {
    return (this.entityIds == null) ? null : this.entityIds.iterator();
  }

  public void addToEntityIds(String elem) {
    if (this.entityIds == null) {
      this.entityIds = new ArrayList<String>();
    }
    this.entityIds.add(elem);
  }

  /**
   * @FieldDoc(
   *     description = "查询实体ids",
   *     example = {}
   * )
   */
  public List<String> getEntityIds() {
    return this.entityIds;
  }

  /**
   * @FieldDoc(
   *     description = "查询实体ids",
   *     example = {}
   * )
   */
  public TPredictionRequest setEntityIds(List<String> entityIds) {
    this.entityIds = entityIds;
    return this;
  }

  public void unsetEntityIds() {
    this.entityIds = null;
  }

  /** Returns true if field entityIds is set (has been assigned a value) and false otherwise */
  public boolean isSetEntityIds() {
    return this.entityIds != null;
  }

  public void setEntityIdsIsSet(boolean value) {
    if (!value) {
      this.entityIds = null;
    }
  }

  public int getExtraSize() {
    return (this.extra == null) ? 0 : this.extra.size();
  }

  public void putToExtra(String key, String val) {
    if (this.extra == null) {
      this.extra = new HashMap<String,String>();
    }
    this.extra.put(key, val);
  }

  /**
   * @FieldDoc(
   *     description = "额外信息",
   *     example = {}
   * )
   */
  public Map<String,String> getExtra() {
    return this.extra;
  }

  /**
   * @FieldDoc(
   *     description = "额外信息",
   *     example = {}
   * )
   */
  public TPredictionRequest setExtra(Map<String,String> extra) {
    this.extra = extra;
    return this;
  }

  public void unsetExtra() {
    this.extra = null;
  }

  /** Returns true if field extra is set (has been assigned a value) and false otherwise */
  public boolean isSetExtra() {
    return this.extra != null;
  }

  public void setExtraIsSet(boolean value) {
    if (!value) {
      this.extra = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case BIZ_CODE:
      if (value == null) {
        unsetBizCode();
      } else {
        setBizCode((String)value);
      }
      break;

    case ENTITY_IDS:
      if (value == null) {
        unsetEntityIds();
      } else {
        setEntityIds((List<String>)value);
      }
      break;

    case EXTRA:
      if (value == null) {
        unsetExtra();
      } else {
        setExtra((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case BIZ_CODE:
      return getBizCode();

    case ENTITY_IDS:
      return getEntityIds();

    case EXTRA:
      return getExtra();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case BIZ_CODE:
      return isSetBizCode();
    case ENTITY_IDS:
      return isSetEntityIds();
    case EXTRA:
      return isSetExtra();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TPredictionRequest)
      return this.equals((TPredictionRequest)that);
    return false;
  }

  public boolean equals(TPredictionRequest that) {
    if (that == null)
      return false;

    boolean this_present_bizCode = true && this.isSetBizCode();
    boolean that_present_bizCode = true && that.isSetBizCode();
    if (this_present_bizCode || that_present_bizCode) {
      if (!(this_present_bizCode && that_present_bizCode))
        return false;
      if (!this.bizCode.equals(that.bizCode))
        return false;
    }

    boolean this_present_entityIds = true && this.isSetEntityIds();
    boolean that_present_entityIds = true && that.isSetEntityIds();
    if (this_present_entityIds || that_present_entityIds) {
      if (!(this_present_entityIds && that_present_entityIds))
        return false;
      if (!this.entityIds.equals(that.entityIds))
        return false;
    }

    boolean this_present_extra = true && this.isSetExtra();
    boolean that_present_extra = true && that.isSetExtra();
    if (this_present_extra || that_present_extra) {
      if (!(this_present_extra && that_present_extra))
        return false;
      if (!this.extra.equals(that.extra))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    return 0;
  }

  public int compareTo(TPredictionRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;
    TPredictionRequest typedOther = (TPredictionRequest)other;

    lastComparison = Boolean.valueOf(isSetBizCode()).compareTo(typedOther.isSetBizCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBizCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bizCode, typedOther.bizCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetEntityIds()).compareTo(typedOther.isSetEntityIds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEntityIds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.entityIds, typedOther.entityIds);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtra()).compareTo(typedOther.isSetExtra());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtra()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extra, typedOther.extra);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TPredictionRequest(");
    boolean first = true;

    sb.append("bizCode:");
    if (this.bizCode == null) {
      sb.append("null");
    } else {
      sb.append(this.bizCode);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("entityIds:");
    if (this.entityIds == null) {
      sb.append("null");
    } else {
      sb.append(this.entityIds);
    }
    first = false;
    if (isSetExtra()) {
      if (!first) sb.append(", ");
      sb.append("extra:");
      if (this.extra == null) {
        sb.append("null");
      } else {
        sb.append(this.extra);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (bizCode == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'bizCode' was not present! Struct: " + toString());
    }
    if (entityIds == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'entityIds' was not present! Struct: " + toString());
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TPredictionRequestStandardSchemeFactory implements SchemeFactory {
    public TPredictionRequestStandardScheme getScheme() {
      return new TPredictionRequestStandardScheme();
    }
  }

  private static class TPredictionRequestStandardScheme extends StandardScheme<TPredictionRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TPredictionRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BIZ_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.bizCode = iprot.readString();
              struct.setBizCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ENTITY_IDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list40 = iprot.readListBegin();
                struct.entityIds = new ArrayList<String>(_list40.size);
                for (int _i41 = 0; _i41 < _list40.size; ++_i41)
                {
                  String _elem42; // required
                  _elem42 = iprot.readString();
                  struct.entityIds.add(_elem42);
                }
                iprot.readListEnd();
              }
              struct.setEntityIdsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // EXTRA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map43 = iprot.readMapBegin();
                struct.extra = new HashMap<String,String>(2*_map43.size);
                for (int _i44 = 0; _i44 < _map43.size; ++_i44)
                {
                  String _key45; // required
                  String _val46; // required
                  _key45 = iprot.readString();
                  _val46 = iprot.readString();
                  struct.extra.put(_key45, _val46);
                }
                iprot.readMapEnd();
              }
              struct.setExtraIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TPredictionRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.bizCode != null) {
        oprot.writeFieldBegin(BIZ_CODE_FIELD_DESC);
        oprot.writeString(struct.bizCode);
        oprot.writeFieldEnd();
      }
      if (struct.entityIds != null) {
        oprot.writeFieldBegin(ENTITY_IDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.entityIds.size()));
          for (String _iter47 : struct.entityIds)
          {
            oprot.writeString(_iter47);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.extra != null) {
        if (struct.isSetExtra()) {
          oprot.writeFieldBegin(EXTRA_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extra.size()));
            for (Map.Entry<String, String> _iter48 : struct.extra.entrySet())
            {
              oprot.writeString(_iter48.getKey());
              oprot.writeString(_iter48.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TPredictionRequestTupleSchemeFactory implements SchemeFactory {
    public TPredictionRequestTupleScheme getScheme() {
      return new TPredictionRequestTupleScheme();
    }
  }

  private static class TPredictionRequestTupleScheme extends TupleScheme<TPredictionRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TPredictionRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeString(struct.bizCode);
      {
        oprot.writeI32(struct.entityIds.size());
        for (String _iter49 : struct.entityIds)
        {
          oprot.writeString(_iter49);
        }
      }
      BitSet optionals = new BitSet();
      if (struct.isSetExtra()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetExtra()) {
        {
          oprot.writeI32(struct.extra.size());
          for (Map.Entry<String, String> _iter50 : struct.extra.entrySet())
          {
            oprot.writeString(_iter50.getKey());
            oprot.writeString(_iter50.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TPredictionRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.bizCode = iprot.readString();
      struct.setBizCodeIsSet(true);
      {
        org.apache.thrift.protocol.TList _list51 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
        struct.entityIds = new ArrayList<String>(_list51.size);
        for (int _i52 = 0; _i52 < _list51.size; ++_i52)
        {
          String _elem53; // required
          _elem53 = iprot.readString();
          struct.entityIds.add(_elem53);
        }
      }
      struct.setEntityIdsIsSet(true);
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TMap _map54 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extra = new HashMap<String,String>(2*_map54.size);
          for (int _i55 = 0; _i55 < _map54.size; ++_i55)
          {
            String _key56; // required
            String _val57; // required
            _key56 = iprot.readString();
            _val57 = iprot.readString();
            struct.extra.put(_key56, _val57);
          }
        }
        struct.setExtraIsSet(true);
      }
    }
  }

}

