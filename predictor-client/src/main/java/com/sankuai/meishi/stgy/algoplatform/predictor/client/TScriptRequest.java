/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.sankuai.meishi.stgy.algoplatform.predictor.client;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @TypeDoc(
 *     description = "请求"
 * )
 */
public class TScriptRequest implements org.apache.thrift.TBase<TScriptRequest, TScriptRequest._Fields>, java.io.Serializable, Cloneable {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TScriptRequest");

  private static final org.apache.thrift.protocol.TField BIZ_CODES_FIELD_DESC = new org.apache.thrift.protocol.TField("bizCodes", org.apache.thrift.protocol.TType.LIST, (short)1);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TScriptRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TScriptRequestTupleSchemeFactory());
  }

  /**
   * @FieldDoc(
   *     description = "bizCode集合",
   *     example = {}
   * )
   */
  public List<String> bizCodes; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * @FieldDoc(
     *     description = "bizCode集合",
     *     example = {}
     * )
     */
    BIZ_CODES((short)1, "bizCodes");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BIZ_CODES
          return BIZ_CODES;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private _Fields optionals[] = {_Fields.BIZ_CODES};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BIZ_CODES, new org.apache.thrift.meta_data.FieldMetaData("bizCodes", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TScriptRequest.class, metaDataMap);
  }

  public TScriptRequest() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TScriptRequest(TScriptRequest other) {
    if (other.isSetBizCodes()) {
      List<String> __this__bizCodes = new ArrayList<String>();
      for (String other_element : other.bizCodes) {
        __this__bizCodes.add(other_element);
      }
      this.bizCodes = __this__bizCodes;
    }
  }

  public TScriptRequest deepCopy() {
    return new TScriptRequest(this);
  }

  @Override
  public void clear() {
    this.bizCodes = null;
  }

  public int getBizCodesSize() {
    return (this.bizCodes == null) ? 0 : this.bizCodes.size();
  }

  public java.util.Iterator<String> getBizCodesIterator() {
    return (this.bizCodes == null) ? null : this.bizCodes.iterator();
  }

  public void addToBizCodes(String elem) {
    if (this.bizCodes == null) {
      this.bizCodes = new ArrayList<String>();
    }
    this.bizCodes.add(elem);
  }

  /**
   * @FieldDoc(
   *     description = "bizCode集合",
   *     example = {}
   * )
   */
  public List<String> getBizCodes() {
    return this.bizCodes;
  }

  /**
   * @FieldDoc(
   *     description = "bizCode集合",
   *     example = {}
   * )
   */
  public TScriptRequest setBizCodes(List<String> bizCodes) {
    this.bizCodes = bizCodes;
    return this;
  }

  public void unsetBizCodes() {
    this.bizCodes = null;
  }

  /** Returns true if field bizCodes is set (has been assigned a value) and false otherwise */
  public boolean isSetBizCodes() {
    return this.bizCodes != null;
  }

  public void setBizCodesIsSet(boolean value) {
    if (!value) {
      this.bizCodes = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case BIZ_CODES:
      if (value == null) {
        unsetBizCodes();
      } else {
        setBizCodes((List<String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case BIZ_CODES:
      return getBizCodes();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case BIZ_CODES:
      return isSetBizCodes();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TScriptRequest)
      return this.equals((TScriptRequest)that);
    return false;
  }

  public boolean equals(TScriptRequest that) {
    if (that == null)
      return false;

    boolean this_present_bizCodes = true && this.isSetBizCodes();
    boolean that_present_bizCodes = true && that.isSetBizCodes();
    if (this_present_bizCodes || that_present_bizCodes) {
      if (!(this_present_bizCodes && that_present_bizCodes))
        return false;
      if (!this.bizCodes.equals(that.bizCodes))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    return 0;
  }

  public int compareTo(TScriptRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;
    TScriptRequest typedOther = (TScriptRequest)other;

    lastComparison = Boolean.valueOf(isSetBizCodes()).compareTo(typedOther.isSetBizCodes());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBizCodes()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bizCodes, typedOther.bizCodes);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TScriptRequest(");
    boolean first = true;

    if (isSetBizCodes()) {
      sb.append("bizCodes:");
      if (this.bizCodes == null) {
        sb.append("null");
      } else {
        sb.append(this.bizCodes);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TScriptRequestStandardSchemeFactory implements SchemeFactory {
    public TScriptRequestStandardScheme getScheme() {
      return new TScriptRequestStandardScheme();
    }
  }

  private static class TScriptRequestStandardScheme extends StandardScheme<TScriptRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TScriptRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BIZ_CODES
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list96 = iprot.readListBegin();
                struct.bizCodes = new ArrayList<String>(_list96.size);
                for (int _i97 = 0; _i97 < _list96.size; ++_i97)
                {
                  String _elem98; // required
                  _elem98 = iprot.readString();
                  struct.bizCodes.add(_elem98);
                }
                iprot.readListEnd();
              }
              struct.setBizCodesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TScriptRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.bizCodes != null) {
        if (struct.isSetBizCodes()) {
          oprot.writeFieldBegin(BIZ_CODES_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.bizCodes.size()));
            for (String _iter99 : struct.bizCodes)
            {
              oprot.writeString(_iter99);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TScriptRequestTupleSchemeFactory implements SchemeFactory {
    public TScriptRequestTupleScheme getScheme() {
      return new TScriptRequestTupleScheme();
    }
  }

  private static class TScriptRequestTupleScheme extends TupleScheme<TScriptRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TScriptRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetBizCodes()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetBizCodes()) {
        {
          oprot.writeI32(struct.bizCodes.size());
          for (String _iter100 : struct.bizCodes)
          {
            oprot.writeString(_iter100);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TScriptRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list101 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.bizCodes = new ArrayList<String>(_list101.size);
          for (int _i102 = 0; _i102 < _list101.size; ++_i102)
          {
            String _elem103; // required
            _elem103 = iprot.readString();
            struct.bizCodes.add(_elem103);
          }
        }
        struct.setBizCodesIsSet(true);
      }
    }
  }

}

