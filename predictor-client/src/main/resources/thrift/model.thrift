namespace java com.sankuai.meishi.stgy.algoplatform.predictor.client

/**
 * @TypeDoc(
 *     description = "预测请求"
 * )
 */
struct TPredictRequest {
        /**
         * @FieldDoc(
         *     description = "业务Code",
         *     example = {}
         * )
         */
        1: required string bizCode;
        /**
         * @FieldDoc(
         *     description = "用于ABTest的Key",
         *     example = {}
         * )
         */
        2: optional string abtestKey;
        /**
         * @FieldDoc(
         *     description = "算法预测请求",
         *     example = {}
         * )
         */
        3: optional map<string, string> req;
        /**
         * @FieldDoc(
         *     description = "额外信息",
         *     example = {}
         * )
         */
        4: optional map<string, string> extra;
}

/**
 * @TypeDoc(
 *     description = "预测返回"
 * )
 */
struct TPredictResponse {
        /**
         * @FieldDoc(
         *     description = "状态码，0：正常",
         *     example = {}
         * )
         */
        1: required i32 code;
        /**
         * @FieldDoc(
         *     description = "状态消息",
         *     example = {}
         * )
         */
        2: optional string message;
        /**
         * @FieldDoc(
         *     description = "算法预测返回",
         *     example = {}
         * )
         */
        4: optional map<string, string> data;
        /**
         * @FieldDoc(
         *     description = "额外信息",
         *     example = {}
         * )
         */
        5: optional map<string, string> extra;
}

/**
 * @TypeDoc(
 *     description = "请求"
 * )
 */
struct TPredictionRequest {
        /**
         * @FieldDoc(
         *     description = "业务标识",
         *     example = {}
         * )
         */
        1: required string bizCode;
        /**
         * @FieldDoc(
         *     description = "查询实体ids",
         *     example = {}
         * )
         */
        2: required list<string> entityIds;
        /**
         * @FieldDoc(
         *     description = "额外信息",
         *     example = {}
         * )
         */
        3: optional map<string, string> extra;
}

/**
 * @TypeDoc(
 *     description = "预测值"
 * )
 */
struct TPredictionValue {
        /**
         * @FieldDoc(
         *     description = "预测值",
         *     example = {}
         * )
         */
        1: required map<string, string> values;
}

/**
 * @TypeDoc(
 *     description = "返回"
 * )
 */
struct TPredictionResponse {
        /**
         * @FieldDoc(
         *     description = "正常为0，非0为异常",
         *     example = {}
         * )
         */
        1: required i32 code;
        /**
         * @FieldDoc(
         *     description = "正常为0，非0为异常",
         *     example = {}
         * )
         */
        2: optional string message;
        /**
         * @FieldDoc(
         *     description = "预测值：data.key: entityId, data.values：算法预测值",
         *     example = {}
         * )
         */
        3: optional map<string, list<TPredictionValue>> data;
        /**
         * @FieldDoc(
         *     description = "扩展信息",
         *     example = {}
         * )
         */
        4: optional map<string, string> extra;
}

/**
 * @TypeDoc(
 *     description = "请求"
 * )
 */
struct TInvokeDirectlyRequest {
        /**
         * @FieldDoc(
         *     description = "解释器",
         *     example = {}
         * )
         */
        1: required string interpreter;
        /**
         * @FieldDoc(
         *     description = "算法包路径",
         *     example = {}
         * )
         */
        2: required string path;
        /**
         * @FieldDoc(
         *     description = "调用方法",
         *     example = {}
         * )
         */
        3: required string method;
        /**
         * @FieldDoc(
         *     description = "入参数据",
         *     example = {}
         * )
         */
        4: required string data;
}

/**
 * @TypeDoc(
 *     description = "返回"
 * )
 */
struct TInvokeDirectlyResponse {
        /**
         * @FieldDoc(
         *     description = "时间花费",
         *     example = {}
         * )
         */
        1: required i64 cost;
        /**
         * @FieldDoc(
         *     description = "返回数据",
         *     example = {}
         * )
         */
        2: required string data;
}

/**
 * @TypeDoc(
 *     description = "请求"
 * )
 */
struct TScriptRequest {
        /**
         * @FieldDoc(
         *     description = "bizCode集合",
         *     example = {}
         * )
         */
        1: optional list<string> bizCodes;
}

/**
 * @TypeDoc(
 *     description = "返回"
 * )
 */
struct TScriptResponse {
        /**
         * @FieldDoc(
         *     description = "正常为0，非0为异常",
         *     example = {}
         * )
         */
        1: required i32 code;
        /**
         * @FieldDoc(
         *     description = "返回数据",
         *     example = {}
         * )
         */
        2: optional map<string, string> scriptMap;
}

/**
 * @InterfaceDoc(
 *     type = "octo.thrift",
 *     displayName = "预测服务",
 *     description = "预测服务",
 *     scenarios = "预测服务"
 * )
 */
service TPredictService {
        /**
         * @MethodDoc(
         *     displayName = "predict",
         *     description = "predict",
         *     parameters = {
         *         @ParamDoc( name = "req", description = "请求", example = {})
         *     },
         *     returnValueDescription = "返回",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：OCTO")
         *     }
         * )
         */
        TPredictResponse predict(1: required TPredictRequest req);

        /**
         * @MethodDoc(
         *     displayName = "queryPredictions",
         *     description = "算法预计算结果查询",
         *     parameters = {
         *         @ParamDoc( name = "req", description = "请求", example = {})
         *     },
         *     returnValueDescription = "返回",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：OCTO")
         *     }
         * )
         */
        TPredictionResponse queryPredictions(1: TPredictionRequest req);

        /**
         * @MethodDoc(
         *     displayName = "queryScript",
         *     description = "获取脚本参数",
         *     parameters = {
         *         @ParamDoc( name = "req", description = "请求", example = {})
         *     },
         *     returnValueDescription = "返回",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：OCTO")
         *     }
         * )
         */
        TScriptResponse queryScript(1: TScriptRequest req);

        /**
         * @MethodDoc(
         *     displayName = "invokeDirectly",
         *     description = "算法包直接调用",
         *     parameters = {
         *         @ParamDoc( name = "req", description = "请求", example = {})
         *     },
         *     returnValueDescription = "返回",
         *     extensions = {
         *         @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：OCTO")
         *     }
         * )
         */
         TInvokeDirectlyResponse invokeDirectly(1: TInvokeDirectlyRequest req);
}