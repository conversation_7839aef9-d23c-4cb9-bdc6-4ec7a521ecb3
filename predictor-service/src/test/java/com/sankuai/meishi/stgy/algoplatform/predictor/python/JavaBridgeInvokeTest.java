package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.alibaba.fastjson.JSONObject;
import com.dianping.pigeon.remoting.common.service.GenericService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory;
import com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceInfo;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftServiceInfo;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * JavaBridge中pigeonInvoke和thriftInvoke方法的专项测试
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
@PrepareForTest({SpringContextUtils.class})
public class JavaBridgeInvokeTest {

    @Mock
    private PigeonServiceFactory mockPigeonServiceFactory;

    @Mock
    private GenericService mockPigeonGenericService;

    @Mock
    private ThriftClientProxyBeanConfig mockThriftConfig;

    @Mock
    private ThriftClientProxy mockThriftProxy;

    @Mock
    private com.meituan.service.mobile.mtthrift.generic.GenericService mockThriftGenericService;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(SpringContextUtils.class);
    }

    // ==================== pigeonInvoke 测试用例 ====================

    /**
     * 测试pigeonInvoke - 正常调用成功
     */
    @Test
    public void testPigeonInvoke_Success() throws Exception {
        // arrange
        String serviceInfo = createPigeonServiceInfoJson("com.sankuai.test", "com.test.Service", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String", "java.lang.Integer");
        List<String> paramValues = Arrays.asList("testParam", "123");
        String expectedResult = "pigeon invoke success";

        when(SpringContextUtils.getBean(PigeonServiceFactory.class)).thenReturn(mockPigeonServiceFactory);
        when(mockPigeonServiceFactory.createPigeonProxy(any(PigeonServiceInfo.class))).thenReturn(mockPigeonGenericService);
        when(mockPigeonGenericService.$invoke("testMethod", paramsType, paramValues)).thenReturn(expectedResult);

        // act
        String result = JavaBridge.pigeonInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertEquals(expectedResult, result);
        verify(mockPigeonServiceFactory).createPigeonProxy(any(PigeonServiceInfo.class));
        verify(mockPigeonGenericService).$invoke("testMethod", paramsType, paramValues);
    }

    /**
     * 测试pigeonInvoke - 服务信息JSON格式错误
     */
    @Test
    public void testPigeonInvoke_InvalidServiceInfoJson() {
        // arrange
        String invalidServiceInfo = "{invalid json}";
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        // act
        String result = JavaBridge.pigeonInvoke(invalidServiceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试pigeonInvoke - 创建代理失败
     */
    @Test
    public void testPigeonInvoke_CreateProxyFailed() throws Exception {
        // arrange
        String serviceInfo = createPigeonServiceInfoJson("com.sankuai.test", "com.test.Service", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        when(SpringContextUtils.getBean(PigeonServiceFactory.class)).thenReturn(mockPigeonServiceFactory);
        when(mockPigeonServiceFactory.createPigeonProxy(any(PigeonServiceInfo.class)))
            .thenThrow(new RuntimeException("Failed to create pigeon proxy"));

        // act
        String result = JavaBridge.pigeonInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试pigeonInvoke - 服务调用失败
     */
    @Test
    public void testPigeonInvoke_ServiceCallFailed() throws Exception {
        // arrange
        String serviceInfo = createPigeonServiceInfoJson("com.sankuai.test", "com.test.Service", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        when(SpringContextUtils.getBean(PigeonServiceFactory.class)).thenReturn(mockPigeonServiceFactory);
        when(mockPigeonServiceFactory.createPigeonProxy(any(PigeonServiceInfo.class))).thenReturn(mockPigeonGenericService);
        when(mockPigeonGenericService.$invoke(anyString(), anyList(), anyList()))
            .thenThrow(new RuntimeException("Service call failed"));

        // act
        String result = JavaBridge.pigeonInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试pigeonInvoke - 空参数列表
     */
    @Test
    public void testPigeonInvoke_EmptyParams() throws Exception {
        // arrange
        String serviceInfo = createPigeonServiceInfoJson("com.sankuai.test", "com.test.Service", "testMethod", 5000);
        List<String> paramsType = Collections.emptyList();
        List<String> paramValues = Collections.emptyList();
        String expectedResult = "empty params result";

        when(SpringContextUtils.getBean(PigeonServiceFactory.class)).thenReturn(mockPigeonServiceFactory);
        when(mockPigeonServiceFactory.createPigeonProxy(any(PigeonServiceInfo.class))).thenReturn(mockPigeonGenericService);
        when(mockPigeonGenericService.$invoke("testMethod", paramsType, paramValues)).thenReturn(expectedResult);

        // act
        String result = JavaBridge.pigeonInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertEquals(expectedResult, result);
    }

    // ==================== thriftInvoke 测试用例 ====================

    /**
     * 测试thriftInvoke - 正常调用成功
     */
    @Test
    public void testThriftInvoke_Success() throws Exception {
        // arrange
        String serviceInfo = createThriftServiceInfoJson("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String", "java.lang.Integer");
        List<String> paramValues = Arrays.asList("testParam", "123");
        String expectedResult = "thrift invoke success";

        when(SpringContextUtils.getBean(ThriftClientProxyBeanConfig.class)).thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any(ThriftServiceInfo.class))).thenReturn(mockThriftProxy);
        when(mockThriftProxy.getObject()).thenReturn(mockThriftGenericService);
        when(mockThriftGenericService.$invoke("testMethod", paramsType, paramValues)).thenReturn(expectedResult);

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertEquals(expectedResult, result);
        verify(mockThriftConfig).createThriftProxy(any(ThriftServiceInfo.class));
        verify(mockThriftProxy).getObject();
        verify(mockThriftGenericService).$invoke("testMethod", paramsType, paramValues);
    }

    /**
     * 测试thriftInvoke - 服务信息JSON格式错误
     */
    @Test
    public void testThriftInvoke_InvalidServiceInfoJson() {
        // arrange
        String invalidServiceInfo = "{invalid json}";
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        // act
        Object result = JavaBridge.thriftInvoke(invalidServiceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试thriftInvoke - 创建代理失败
     */
    @Test
    public void testThriftInvoke_CreateProxyFailed() throws Exception {
        // arrange
        String serviceInfo = createThriftServiceInfoJson("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        when(SpringContextUtils.getBean(ThriftClientProxyBeanConfig.class)).thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any(ThriftServiceInfo.class)))
            .thenThrow(new RuntimeException("Failed to create thrift proxy"));

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试thriftInvoke - 获取服务对象失败
     */
    @Test
    public void testThriftInvoke_GetObjectFailed() throws Exception {
        // arrange
        String serviceInfo = createThriftServiceInfoJson("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        when(SpringContextUtils.getBean(ThriftClientProxyBeanConfig.class)).thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any(ThriftServiceInfo.class))).thenReturn(mockThriftProxy);
        when(mockThriftProxy.getObject()).thenThrow(new RuntimeException("Failed to get service object"));

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试thriftInvoke - 服务调用失败
     */
    @Test
    public void testThriftInvoke_ServiceCallFailed() throws Exception {
        // arrange
        String serviceInfo = createThriftServiceInfoJson("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        when(SpringContextUtils.getBean(ThriftClientProxyBeanConfig.class)).thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any(ThriftServiceInfo.class))).thenReturn(mockThriftProxy);
        when(mockThriftProxy.getObject()).thenReturn(mockThriftGenericService);
        when(mockThriftGenericService.$invoke(anyString(), anyList(), anyList()))
            .thenThrow(new RuntimeException("Service call failed"));

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试thriftInvoke - 空参数列表
     */
    @Test
    public void testThriftInvoke_EmptyParams() throws Exception {
        // arrange
        String serviceInfo = createThriftServiceInfoJson("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000);
        List<String> paramsType = Collections.emptyList();
        List<String> paramValues = Collections.emptyList();
        String expectedResult = "empty params result";

        when(SpringContextUtils.getBean(ThriftClientProxyBeanConfig.class)).thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any(ThriftServiceInfo.class))).thenReturn(mockThriftProxy);
        when(mockThriftProxy.getObject()).thenReturn(mockThriftGenericService);
        when(mockThriftGenericService.$invoke("testMethod", paramsType, paramValues)).thenReturn(expectedResult);

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertEquals(expectedResult, result);
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建PigeonServiceInfo的JSON字符串
     */
    private String createPigeonServiceInfoJson(String appKey, String interfaceName, String methodName, int timeout) {
        PigeonServiceInfo serviceInfo = new PigeonServiceInfo();
        serviceInfo.setAppKey(appKey);
        serviceInfo.setInterfaceName(interfaceName);
        serviceInfo.setMethodName(methodName);
        serviceInfo.setTimeout(timeout);
        return JSONObject.toJSONString(serviceInfo);
    }

    /**
     * 创建ThriftServiceInfo的JSON字符串
     */
    private String createThriftServiceInfoJson(String appKey, String interfaceName, String methodName, int timeOut) {
        ThriftServiceInfo serviceInfo = new ThriftServiceInfo();
        serviceInfo.setAppKey(appKey);
        serviceInfo.setInterfaceName(interfaceName);
        serviceInfo.setMethodName(methodName);
        serviceInfo.setTimeOut(timeOut);
        return JSONObject.toJSONString(serviceInfo);
    }

    // ==================== 边界情况和异常测试 ====================

    /**
     * 测试pigeonInvoke - null参数
     */
    @Test
    public void testPigeonInvoke_NullParams() {
        // arrange
        String serviceInfo = createPigeonServiceInfoJson("com.sankuai.test", "com.test.Service", "testMethod", 5000);

        // act
        String result1 = JavaBridge.pigeonInvoke(null, Arrays.asList("java.lang.String"), Arrays.asList("test"));
        String result2 = JavaBridge.pigeonInvoke(serviceInfo, null, Arrays.asList("test"));
        String result3 = JavaBridge.pigeonInvoke(serviceInfo, Arrays.asList("java.lang.String"), null);

        // assert
        assertNull(result1);
        assertNull(result2);
        assertNull(result3);
    }

    /**
     * 测试thriftInvoke - null参数
     */
    @Test
    public void testThriftInvoke_NullParams() {
        // arrange
        String serviceInfo = createThriftServiceInfoJson("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000);

        // act
        Object result1 = JavaBridge.thriftInvoke(null, Arrays.asList("java.lang.String"), Arrays.asList("test"));
        Object result2 = JavaBridge.thriftInvoke(serviceInfo, null, Arrays.asList("test"));
        Object result3 = JavaBridge.thriftInvoke(serviceInfo, Arrays.asList("java.lang.String"), null);

        // assert
        assertNull(result1);
        assertNull(result2);
        assertNull(result3);
    }

    /**
     * 测试pigeonInvoke - 参数类型和值数量不匹配
     */
    @Test
    public void testPigeonInvoke_ParamsMismatch() throws Exception {
        // arrange
        String serviceInfo = createPigeonServiceInfoJson("com.sankuai.test", "com.test.Service", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String", "java.lang.Integer");
        List<String> paramValues = Arrays.asList("testParam"); // 只有一个值，但类型有两个

        when(SpringContextUtils.getBean(PigeonServiceFactory.class)).thenReturn(mockPigeonServiceFactory);
        when(mockPigeonServiceFactory.createPigeonProxy(any(PigeonServiceInfo.class))).thenReturn(mockPigeonGenericService);
        when(mockPigeonGenericService.$invoke(anyString(), anyList(), anyList()))
            .thenThrow(new IllegalArgumentException("Parameter count mismatch"));

        // act
        String result = JavaBridge.pigeonInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试thriftInvoke - 参数类型和值数量不匹配
     */
    @Test
    public void testThriftInvoke_ParamsMismatch() throws Exception {
        // arrange
        String serviceInfo = createThriftServiceInfoJson("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String", "java.lang.Integer");
        List<String> paramValues = Arrays.asList("testParam"); // 只有一个值，但类型有两个

        when(SpringContextUtils.getBean(ThriftClientProxyBeanConfig.class)).thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any(ThriftServiceInfo.class))).thenReturn(mockThriftProxy);
        when(mockThriftProxy.getObject()).thenReturn(mockThriftGenericService);
        when(mockThriftGenericService.$invoke(anyString(), anyList(), anyList()))
            .thenThrow(new IllegalArgumentException("Parameter count mismatch"));

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试pigeonInvoke - 返回null结果
     */
    @Test
    public void testPigeonInvoke_NullResult() throws Exception {
        // arrange
        String serviceInfo = createPigeonServiceInfoJson("com.sankuai.test", "com.test.Service", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        when(SpringContextUtils.getBean(PigeonServiceFactory.class)).thenReturn(mockPigeonServiceFactory);
        when(mockPigeonServiceFactory.createPigeonProxy(any(PigeonServiceInfo.class))).thenReturn(mockPigeonGenericService);
        when(mockPigeonGenericService.$invoke("testMethod", paramsType, paramValues)).thenReturn(null);

        // act
        String result = JavaBridge.pigeonInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试thriftInvoke - 返回null结果
     */
    @Test
    public void testThriftInvoke_NullResult() throws Exception {
        // arrange
        String serviceInfo = createThriftServiceInfoJson("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000);
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        when(SpringContextUtils.getBean(ThriftClientProxyBeanConfig.class)).thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any(ThriftServiceInfo.class))).thenReturn(mockThriftProxy);
        when(mockThriftProxy.getObject()).thenReturn(mockThriftGenericService);
        when(mockThriftGenericService.$invoke("testMethod", paramsType, paramValues)).thenReturn(null);

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }
}
