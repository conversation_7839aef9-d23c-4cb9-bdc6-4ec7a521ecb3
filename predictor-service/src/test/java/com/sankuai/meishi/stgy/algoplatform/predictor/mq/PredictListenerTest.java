package com.sankuai.meishi.stgy.algoplatform.predictor.mq;

import com.dianping.lion.common.util.JsonUtils;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.TPredictServicePublish;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.CompressUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class PredictListenerTest {

    @InjectMocks
    private PredictListener predictListener;

    @Mock
    private TPredictServicePublish tPredictServicePublish;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常情况下消息被成功处理
     */
    @Test
    public void testRecvMessageSuccess() throws Throwable {
        // arrange
        String validMessageBody = "{\"someKey\":\"someValue\"}";
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, validMessageBody);
        MessagetContext messagetContext = new MessagetContext();
        // act
        ConsumeStatus result = predictListener.recvMessage(message, messagetContext);
        // assert
        verify(tPredictServicePublish).predictAsync(any(TPredictRequest.class));
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试消息体无法解析为TPredictRequest时的情况
     */
    @Test
    public void testRecvMessageParseFailure() throws Throwable {
        // arrange
        String invalidMessageBody = "invalid JSON";
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, invalidMessageBody);
        MessagetContext messagetContext = new MessagetContext();
        // act
        ConsumeStatus result = predictListener.recvMessage(message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
    }

    /**
     * 测试predictAsync方法抛出异常时的情况
     */
    @Test
    public void testRecvMessagePredictAsyncThrowsException() throws Throwable {
        // arrange
        String validMessageBody = "{\"someKey\":\"someValue\"}";
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, validMessageBody);
        MessagetContext messagetContext = new MessagetContext();
        doThrow(new RuntimeException()).when(tPredictServicePublish).predictAsync(any(TPredictRequest.class));
        // act
        ConsumeStatus result = predictListener.recvMessage(message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
    }

    /**
     * 测试消息体需要压缩的情况
     */
    @Test
    public void testRecvMessagePredictCompress() throws Throwable {
        // arrange
        String validMessageBody = "{\"bizCode\":\"zb_sameGoodsMatch\",\"extra\":{\"unique_key\":\"e6d92db534c1e173d585a7cbe304cd7a\",\"partition_date\":\"2024-10-05 07:00:00\",\"source\":\"udf\",\"version\":\"1728057600\",\"dj_poi_id\":\"tT/DY9BOSrgUExnZjbHRAaTF0vd4yGLCzUR5upZlCDQ=\",\"data_source\":\"dj_poi\"},\"extraSize\":6,\"req\":{\"compressReq\":\"H4sIAAAAAAAAAKtWSs7PLShKLS4OSi1UslIyNDJWqgUA+hqiFhUAAAA=\"}}";
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, validMessageBody);
        MessagetContext messagetContext = new MessagetContext();
        // act
        ConsumeStatus result = predictListener.recvMessage(message, messagetContext);
        // assert
        verify(tPredictServicePublish).predictAsync(any(TPredictRequest.class));
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
