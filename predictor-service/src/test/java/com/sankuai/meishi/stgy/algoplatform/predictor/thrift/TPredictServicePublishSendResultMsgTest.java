package com.sankuai.meishi.stgy.algoplatform.predictor.thrift;

import com.meituan.mafka.client.producer.IProducerProcessor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * TPredictServicePublish.sendResultMsg 私有方法的单元测试
 * 简化版本，主要测试有 resultSendCellName 的情况
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class TPredictServicePublishSendResultMsgTest {

    private TPredictServicePublish tPredictServicePublish;

    @Mock
    private IProducerProcessor predictResultProducer;

    private Method sendResultMsgMethod;

    @Before
    public void setUp() throws Exception {
        // 创建测试对象
        tPredictServicePublish = new TPredictServicePublish();

        // 通过反射注入 mock 的 predictResultProducer
        Field predictResultProducerField = TPredictServicePublish.class.getDeclaredField("predictResultProducer");
        predictResultProducerField.setAccessible(true);
        predictResultProducerField.set(tPredictServicePublish, predictResultProducer);

        // 获取私有方法
        sendResultMsgMethod = TPredictServicePublish.class.getDeclaredMethod(
                "sendResultMsg", Map.class, String.class);
        sendResultMsgMethod.setAccessible(true);
    }

    /**
     * 测试场景1：extra 包含 resultSendCellName 且不为空
     * 预期：使用 predictResultProducer 发送消息
     */
    @Test
    public void testSendResultMsg_WithValidResultSendCellName() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", "test-cell");
        String message = "test message";

        // 执行测试
        sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);

        // 验证结果
        verify(predictResultProducer, times(1)).sendMessage(message);
    }

    /**
     * 测试场景2：extra 包含 resultSendCellName 但值为空字符串
     * 预期：进入 else 分支，但由于无法模拟静态方法，会抛出异常
     */
    @Test(expected = Exception.class)
    public void testSendResultMsg_WithEmptyResultSendCellName() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", "");
        String message = "test message";

        // 执行测试 - 预期会抛出异常，因为无法找到对应的 bean
        sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);
    }

    /**
     * 测试场景3：extra 包含 resultSendCellName 但值为 null
     * 预期：进入 else 分支，但由于无法模拟静态方法，会抛出异常
     */
    @Test(expected = Exception.class)
    public void testSendResultMsg_WithNullResultSendCellName() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", null);
        String message = "test message";

        // 执行测试 - 预期会抛出异常，因为无法找到对应的 bean
        sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);
    }

    /**
     * 测试场景4：extra 为 null
     * 预期：会抛出 NullPointerException，因为代码会尝试调用 extra.get()
     */
    @Test(expected = Exception.class)
    public void testSendResultMsg_WithNullExtra() throws Exception {
        // 准备测试数据
        Map<String, String> extra = null;
        String message = "test message";

        // 执行测试 - 预期抛出 NullPointerException
        sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);
    }

    /**
     * 测试场景5：processor.sendMessage 抛出异常
     * 预期：异常会被传播
     */
    @Test(expected = Exception.class)
    public void testSendResultMsg_ProcessorThrowsException() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", "test-cell");
        String message = "test message";

        // Mock processor 抛出异常
        doThrow(new RuntimeException("Send failed")).when(predictResultProducer).sendMessage(anyString());

        // 执行测试 - 预期抛出异常
        try {
            sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);
        } catch (Exception e) {
            // 检查是否是预期的异常
            if (e.getCause() instanceof RuntimeException) {
                throw (Exception) e.getCause();
            }
            throw e;
        }
    }
}
