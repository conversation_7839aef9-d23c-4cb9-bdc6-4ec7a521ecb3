<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <appenders>
        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，非阻塞模式-->
        <XMDFile name="infoAppender" fileName="info.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} - - [%p] %t %c{1} %XMDT %X{defineOutput} %msg%n"/>
        </XMDFile>

        <XMDFile name="warnAppender" fileName="warn.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} - - [%p] %t %c{1} %XMDT %X{defineOutput} %msg%n"/>
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <XMDFile name="errorAppender" fileName="error.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} - - [%p] %t %c{1} %XMDT %X{defineOutput} %msg%n"/>
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>

        <!--日志远程上报-->
        <AsyncScribe name="ScribeAsyncAppender" blocking="false">
            <!--远程日志默认使用appkey作为日志名(app.properties文件中的app.name字段)，也可自定义scribeCategory属性，scribeCategory优先级高于appkey-->
            <LcLayout/>
        </AsyncScribe>

        <AsyncScribe name="AsyncScribeAppender_com.sankuai.algoplatform.predictor.model.log" blocking="false">
            <Property name="scribeCategory">com.sankuai.algoplatform.predictor.model.log</Property>
            <!-- 如果要开启丢失率检测，请放开下面代码注释 -->
            <!-- <Property name="checkLoss">true</Property> -->
            <LcLayout/>
        </AsyncScribe>

        <CatAppender name="catAppender"/>

        <Scribe name="AB-LOG-SERVER">
            <Property name="scribeCategory">horizon_divide_data</Property>
            <LcLayout/>
        </Scribe>
        <Scribe name="AB-LOG-CLIENT">
            <Property name="scribeCategory">horizon_divide_daily</Property>
            <LcLayout/>
        </Scribe>
    </appenders>

    <loggers>
        <!--远程日志，详细使用说明参见 MDP 文档中日志中心部分 https://km.sankuai.com/custom/onecloud/page/424836119#id-3%E6%97%A5%E5%BF%97%E4%B8%AD%E5%BF%83 -->
        <logger name="scribe" level="info" additivity="false">
            <appender-ref ref="ScribeAsyncAppender"/>
        </logger>

        <logger name="logger_com.sankuai.algoplatform.predictor.model.log" level="info" additivity="false">
            <appender-ref ref="AsyncScribeAppender_com.sankuai.algoplatform.predictor.model.log"/>
        </logger>

        <logger name="ab_server" level="info" additivity="false">
            <appender-ref ref="AB-LOG-SERVER"/>
        </logger>
        <logger name="ab_client" level="info" additivity="false">
            <appender-ref ref="AB-LOG-CLIENT"/>
        </logger>



        <root level="info">
            <appender-ref ref="infoAppender"/>
            <appender-ref ref="warnAppender"/>
            <appender-ref ref="errorAppender"/>
            <appender-ref ref="catAppender"/>
        </root>
    </loggers>
</configuration>