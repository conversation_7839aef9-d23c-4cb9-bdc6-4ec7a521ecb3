package com.sankuai.meishi.stgy.algoplatform.predictor.pigeon;

import com.dianping.pigeon.remoting.ServiceFactory;
import com.dianping.pigeon.remoting.common.domain.GenericType;
import com.dianping.pigeon.remoting.common.service.GenericService;
import com.dianping.pigeon.remoting.invoker.config.InvokerConfig;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年05月26日 16:46
 */
@Slf4j
@Service
public class PigeonServiceFactory {
    public static Map<String, GenericService> pigeonGenericService = Maps.newConcurrentMap();

    public GenericService createPigeonProxy(PigeonServiceInfo pigeonServiceInfo) {
        GenericService genericService = pigeonGenericService.get(getPigeonServiceKey(pigeonServiceInfo));
        if (genericService != null) {
            return genericService;
        }

        int timeout = pigeonServiceInfo.getTimeout() <= 0 ? 6000 : pigeonServiceInfo.getTimeout();
        InvokerConfig<GenericService> invokerConfig = new InvokerConfig<>(pigeonServiceInfo.getInterfaceName(), GenericService.class);
        invokerConfig.setTimeout(timeout);
        invokerConfig.setGeneric(GenericType.JSON_SIMPLE.getName());
        invokerConfig.setCallType("sync");
        invokerConfig.setRemoteAppKey(pigeonServiceInfo.getAppKey());
        if (StringUtils.isNotBlank(pigeonServiceInfo.getCell())) {
            invokerConfig.setCell(pigeonServiceInfo.getCell());
        }
        return pigeonGenericService.computeIfAbsent(
                getPigeonServiceKey(pigeonServiceInfo),
                k -> {
                    // 创建配置逻辑
                    return ServiceFactory.getService(invokerConfig);
                }
        );
    }

    private String getPigeonServiceKey(PigeonServiceInfo pigeonServiceInfo) {
        return pigeonServiceInfo.getAppKey() + "_" + pigeonServiceInfo.getInterfaceName() + "_" + pigeonServiceInfo.getCell() + "_" + pigeonServiceInfo.getTimeout();
    }
}
