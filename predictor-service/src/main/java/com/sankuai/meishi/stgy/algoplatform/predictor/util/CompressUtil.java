package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.Deflater;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.util.zip.InflaterInputStream;

@Slf4j
public class CompressUtil {
    public static final String COMPRESS_TYPE_GZIP = "gzip";
    public static final String COMPRESS_TYPE_DEFLATE_1 = "deflate_1";
    public static final String COMPRESS_TYPE_DEFLATE_9 = "deflate_9";
    public static final String COMPRESS_TYPE_DEFLATE = "deflate";

    public static String compress(String input, String compressType) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        try {
            long t0 = System.currentTimeMillis();

            byte[] resBytes;
            switch (compressType) {
                case COMPRESS_TYPE_GZIP:
                    resBytes = doGzip(input);
                    break;
                case COMPRESS_TYPE_DEFLATE:
                    resBytes = deflate(input, Deflater.DEFAULT_COMPRESSION);
                    break;
                case COMPRESS_TYPE_DEFLATE_9:
                    resBytes = deflate(input, Deflater.BEST_COMPRESSION);
                    break;
                case COMPRESS_TYPE_DEFLATE_1:
                    resBytes = deflate(input, Deflater.BEST_SPEED);
                    break;
                default:
                    log.warn("compress unknown type: {}", compressType);
                    RaptorTrack.Sys_UnexpectedVisitNum.report("unknown_compressType_" + compressType);
                    throw new IllegalArgumentException("compress unknown type: " + compressType);
            }
            String result = base64encode(resBytes);

            long cost = System.currentTimeMillis() - t0;
            double rate = (input.length() + 0.0) / result.length();

            log.info("compress: compressType:{} cost:{}, rate:{}, len({},{})", compressType, cost, rate, input.length(), result.length());
            return result;
        } catch (Exception e) {
            log.error("compress failed, compressType={}, input={}", compressType, input, e);
            throw new RuntimeException(String.format("compress failed: compressType:%s, input:%s", compressType, input), e);
        }
    }

    public static String decompress(String input, String compressType) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        try {
            long t0 = System.currentTimeMillis();

            byte[] dataBytes = base64decode(input);
            if (dataBytes == null || dataBytes.length == 0) {
                return input;
            }

            String result;
            switch (compressType) {
                case COMPRESS_TYPE_GZIP:
                    result = unGzip(dataBytes);
                    break;
                case COMPRESS_TYPE_DEFLATE:
                case COMPRESS_TYPE_DEFLATE_9:
                case COMPRESS_TYPE_DEFLATE_1:
                    result = inflate(dataBytes);
                    break;
                default:
                    log.warn("decompress unknown type: {}", compressType);
                    RaptorTrack.Sys_UnexpectedVisitNum.report("unknown_decompressType_" + compressType);
                    throw new IllegalArgumentException("decompress unknown type: " + compressType);
            }
            long cost = System.currentTimeMillis() - t0;
            log.info("decompress: compressType:{} cost:{}", compressType, cost);
            return result;
        } catch (Exception e) {
            log.error("decompress failed, compressType:{}, input={}", compressType, input, e);
            throw new RuntimeException(String.format("decompress failed, compressType:%s, input=%s", compressType, input), e);
        }
    }

    public static String base64encode(byte[] bytes) {
        return new String(Base64.getEncoder().encode(bytes));
    }

    public static byte[] base64decode(String string) {
        return Base64.getDecoder().decode(string);
    }

    public static byte[] doGzip(String input) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        GZIPOutputStream gzip = new GZIPOutputStream(bos);
        gzip.write(input.getBytes(StandardCharsets.UTF_8));
        gzip.close();
        return bos.toByteArray();
    }

    public static String unGzip(byte[] dataBytes) throws IOException {
        ByteArrayInputStream bis = new ByteArrayInputStream(dataBytes);
        GZIPInputStream unGzip = new GZIPInputStream(bis);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        byte[] buffer = new byte[2048];
        int len;
        while ((len = unGzip.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        unGzip.close();
        return new String(bos.toByteArray(), StandardCharsets.UTF_8);
    }

    public static String inflate(byte[] dataBytes) throws IOException {
        ByteArrayInputStream bis = new ByteArrayInputStream(dataBytes);
        InflaterInputStream iis = new InflaterInputStream(bis);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        byte[] buffer = new byte[2048];
        int len;
        while ((len = iis.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        iis.close();
        return new String(bos.toByteArray(), StandardCharsets.UTF_8);
    }

    public static byte[] deflate(String input, int level) {
        Deflater deflater = new Deflater(level);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            deflater.setInput(input.getBytes(StandardCharsets.UTF_8));
            deflater.finish();
            final byte[] buf = new byte[2048];
            while (!deflater.finished()) {
                int count = deflater.deflate(buf);
                bos.write(buf, 0, count);
            }
            return bos.toByteArray();
        } finally {
            deflater.end();
        }
    }

}
