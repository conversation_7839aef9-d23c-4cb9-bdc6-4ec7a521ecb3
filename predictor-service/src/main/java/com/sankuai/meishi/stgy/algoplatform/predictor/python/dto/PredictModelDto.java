package com.sankuai.meishi.stgy.algoplatform.predictor.python.dto;

import com.sankuai.meishi.stgy.algoplatform.predictor.enumeration.ModelType;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/14 15:12
 **/
@Data
public class PredictModelDto {

    private List<List<String>> input;

    private List<List<String>> processed_input;

    /**
     * 从tokens转化为id的函数类型
     */
    private String func_type;

    /**
     * 模型最大输入长度
     */
    private Integer max_seq_len;

    /**
     * 模型名称
     */
    private String model_name;

    /**
     * 模型类型
     * null 或 1 为TF模型
     * 2 为Torch模型
     */
    private Integer model_type;

    /**
     * mlp特征平台分组id
     */
    private String groupId;

    /**
     * 返回模型输出指定下标的值，不传默认返回第二个（index=1）
     */
    private Integer modelIndex;

    private String mt_poi_id;

    private String dj_poi_id;

    private String mt_deal_id;

    private String dj_deal_id;

    /**
     * 区分不同的任务, 0: 非矩阵任务, 1: 矩阵任务
     */
    private int pre_handle_struct_type;

    /**
     * 区分模型缓存值内容类型，0: 模型缓存值内容类型为Float, 1: 为string，不做处理直接返回
     */
    private int cache_type;

    /**
     * 在python java中透传的参数，主要用于打点
     */
    private Map<String, String> contextData = new HashMap<>();

    public boolean isNotMatrix() {
        return this.pre_handle_struct_type == 0;
    }

    /**
     * 获取调用模型枚举，此处有兜底逻辑
     * 默认兜底为TF模型，兼容餐旧逻辑
     * @return
     */
    public ModelType getModelTypeEnum() {
        Integer model_type = getModel_type();
        // 默认兜底为TF模型，兼容餐旧逻辑
        if (model_type == null) {
            return ModelType.TF;
        }
        ModelType modelType = ModelType.getModelTypeByCode(model_type);
        return modelType;
    }
}
