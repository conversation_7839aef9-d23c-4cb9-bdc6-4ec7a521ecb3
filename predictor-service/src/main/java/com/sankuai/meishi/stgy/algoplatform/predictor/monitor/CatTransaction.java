package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 以下代码的Aspect实现
 * Transaction t = Cat.newTransaction(type, name);
 * Cat.getManager().setTraceMode(true);
 * ...
 * t.setStatus(Event.SUCCESS);
 * t.complete();
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface CatTransaction {

    String DEFAULT_TYPE = "ASPECT_TRANSACTION`";

    /**
     * transaction type
     *
     * @return String
     */
    String type() default "ASPECT_TRANSACTION";

    /**
     * transaction name
     *
     * @return String
     */
    String name() default "";

    String fieldValAsName() default "";
}
