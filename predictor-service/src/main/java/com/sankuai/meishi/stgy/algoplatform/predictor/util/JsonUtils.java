package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;

/**
 * <AUTHOR>
 * @date 2023/11/3 11:38
 **/
public class JsonUtils {


    public static <T> T convertObject(Object source, Class<T> clazz) {

        if (source == null) {
            return null;
        }
        String jsonString = JSON.toJSONString(source);

        T result = JSON.parseObject(jsonString, clazz, Feature.IgnoreAutoType);
        return result;
    }

    public static String toJson(Object obj) {
        // 禁用引用检测，避免转json后出现 $ref, 数据中如果多个变量是同一地址，会被标记为$ref
//        return JSON.toJSONString(obj, SerializerFeature.DisableCircularReferenceDetect);
        return JSON.toJSONString(obj);
    }

    public static JSONObject toJsonObject(Object object) {

        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
        return jsonObject;
    }
}
