package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

import java.util.List;
import java.util.Map;

public interface PredictionQueryAppService {

    PredictionQueryContext queryPredictions(PredictionQueryContext context);

    PredictionQueryContext queryPredictionsByGroovyScript(String script, PredictionQueryContext context);

    Map<String, String> queryScript(List<String> bizCodes);
}
