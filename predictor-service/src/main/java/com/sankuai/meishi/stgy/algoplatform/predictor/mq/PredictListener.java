package com.sankuai.meishi.stgy.algoplatform.predictor.mq;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.google.common.collect.ImmutableList;
import com.google.gson.reflect.TypeToken;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.TPredictServicePublish;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.CompressUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/27 15:59
 **/
@Component
@Slf4j
public class PredictListener implements IMessageListener<String> {


    @Autowired
    private TPredictServicePublish tPredictServicePublish;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext messagetContext) {
        String messageBody = null;
        try {
            messageBody = (String) message.getBody();
            log.info("PredictListener#recvMessage, msgId={}, msgOffset={}", message.getMessageID(), message.getOffset());
            TPredictRequest tPredictRequest = JSON.parseObject(messageBody, TPredictRequest.class);

            //判断请求的bizCode中的Req是否被压缩
//            if (needDeCompress(tPredictRequest)) {
            decompressReq(tPredictRequest);
//            }

            tPredictServicePublish.predictAsync(tPredictRequest);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("PredictListener#recvMessage error: msg:{}, ex:", messageBody, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

    /**
     * 判断是否需要解压缩
     *
     * @param tPredictRequest 预测请求
     * @return 是否需要解压缩
     */
    private boolean needDeCompress(TPredictRequest tPredictRequest) {
        List<String> resultCompressedBizcodeList = Lion.getConfigRepository().getList(LionKeys.MAFKA_COMPRESSED_BIZCODE,
                String.class, ImmutableList.of(""));
        return resultCompressedBizcodeList.contains(tPredictRequest.getBizCode());
    }

    /**
     * 处理压缩请求
     *
     * @param tPredictRequest 预测请求
     */
    private void decompressReq(TPredictRequest tPredictRequest) {
        if (tPredictRequest == null || tPredictRequest.getReq() == null) {
            return;
        }
        Map<String, String> req = tPredictRequest.getReq();
        if (req.containsKey("compressReq") && StringUtils.isNotBlank(req.get("compressReq"))) {
            String compressReq = req.get("compressReq");
            try {
                String deCompressReq = CompressUtil.decompress(compressReq, CompressUtil.COMPRESS_TYPE_GZIP);
                Type type = new TypeToken<Map<String, String>>() {
                }.getType();
                Map<String, String> deCompressReqMap = JSON.parseObject(deCompressReq, type);
                if (deCompressReqMap != null && !deCompressReqMap.isEmpty()) {
                    tPredictRequest.setReq(deCompressReqMap);
                } else {
                    log.warn("Decompressed request map is null or empty, req:{}", JsonUtils.toJson(tPredictRequest));
                }
            } catch (Exception e) {
                String errorMsg = "Error processing compressed request";
                log.error("{}, req:{}", errorMsg, JsonUtils.toJson(tPredictRequest), e);
                throw new RuntimeException(errorMsg, e);
            }
        }
    }

}
