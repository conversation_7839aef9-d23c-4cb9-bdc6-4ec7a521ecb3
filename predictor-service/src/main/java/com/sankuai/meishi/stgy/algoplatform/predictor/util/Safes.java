package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/10
 */
public class Safes {

    public static <T> List<T> of(List<T> list) {
        return Optional.ofNullable(list).orElse(Lists.newArrayListWithCapacity(0));
    }

    public static <K, V> Map<K, V> of(Map<K, V> map) {
        return Optional.ofNullable(map).orElse(Maps.newHashMapWithExpectedSize(0));
    }
}
