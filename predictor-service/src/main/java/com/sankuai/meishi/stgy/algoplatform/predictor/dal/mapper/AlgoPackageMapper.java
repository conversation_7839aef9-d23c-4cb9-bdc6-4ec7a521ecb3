package com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoPackageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AlgoPackageMapper extends MybatisBaseMapper<AlgoPackagePo, AlgoPackageExample, Long> {
    int batchInsert(@Param("list") List<AlgoPackagePo> list);
}