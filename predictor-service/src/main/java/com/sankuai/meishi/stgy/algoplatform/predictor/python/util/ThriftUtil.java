package com.sankuai.meishi.stgy.algoplatform.predictor.python.util;

import static com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants.APPKEY;

import com.google.common.collect.ImmutableList;
import com.meituan.service.mobile.mtthrift.generic.GenericService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class ThriftUtil
{
    public static final ConcurrentHashMap<String, ThriftClientProxy> clientCache =
        new ConcurrentHashMap();

    public static String
    invoke(String appKey, String serviceName, String methodName, List<String> paramsJson)
        throws Exception {
        GenericService genericService = (GenericService)getClientProxy(appKey, serviceName).get();
        return genericService.$invoke(methodName, Collections.emptyList(), paramsJson);
    }

    private static ThriftClientProxy getClientProxy(String appKey, String serviceName)
        throws Exception {
        if (clientCache.contains(serviceName)) {
            return clientCache.get(serviceName);
        }

        synchronized (clientCache) {
            if (!clientCache.contains(serviceName)) {
                ThriftClientProxy clientProxy = new ThriftClientProxy();
                clientProxy.setAppKey(APPKEY);
                clientProxy.setRemoteAppkey(appKey);
                clientProxy.setGenericServiceName(serviceName);
                clientProxy.setFilterByServiceName(true);
                clientProxy.setGeneric("json-simple");
                clientProxy.setNettyIO(true);
                clientProxy.afterPropertiesSet();
                clientCache.put(serviceName, clientProxy);
            }
        }
        return clientCache.get(serviceName);
    }
}
