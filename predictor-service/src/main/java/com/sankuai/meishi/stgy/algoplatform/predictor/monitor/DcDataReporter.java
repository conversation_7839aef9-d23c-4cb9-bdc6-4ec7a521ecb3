package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.BmlDataReportUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.PReportMessage;
import com.sankuai.nib.data.zb.datatrace.platform.sdk.enums.BuEnum;
import com.sankuai.nib.data.zb.datatrace.platform.sdk.enums.DomainEnum;
import com.sankuai.nib.data.zb.datatrace.platform.sdk.util.TraceIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DcDataReporter {

    public static void reportModelInfo(PredictModelDto predictModelDto, List<Map<String, Object>> resp, boolean success) {
        try {
            List<String> dcReportBizCode = Lion.getConfigRepository().getList(LionKeys.DC_NEED_REPORT_BICODE,
                    String.class, new ArrayList<>());
            String bizCode = predictModelDto.getContextData().get("bizCode");
            if (!dcReportBizCode.contains(bizCode)) {
                return;
            }

            if (MdpContextUtils.isOfflineEnv()) {
                String info = "{\"buEnum\":\"DAOCAN\",\"customData\":{\"bizCode\":\"zb_dealMatching\",\"req\":{\"cache_type\":0,\"contextData\":{\"cate\":\"火锅\",\"fullLinkTraceId\":\"***********\",\"bizCode\":\"zb_dealMatching\"},\"func_type\":\"bi_convert\",\"groupId\":\"394\",\"input\":[[\"甄选推荐双人1套餐\",\"欢聚实惠四人餐\"],[\"【必点】火锅搭子|饮品[双人餐]\",\"欢聚实惠四人餐\"],[\"【工作日专享】港式火锅双人餐\",\"欢聚实惠四人餐\"],[\"【尝鲜】招牌1+1单人餐\",\"欢聚实惠四人餐\"],[\"欢聚实惠4人餐\",\"欢聚实惠四人餐\"],[\"甄选推荐双人1套餐\",\"尝鲜|招牌1+1单人餐\"],[\"【必点】火锅搭子|饮品[双人餐]\",\"尝鲜|招牌1+1单人餐\"],[\"【工作日专享】港式火锅双人餐\",\"尝鲜|招牌1+1单人餐\"],[\"【尝鲜】招牌1+1单人餐\",\"尝鲜|招牌1+1单人餐\"],[\"欢聚实惠4人餐\",\"尝鲜|招牌1+1单人餐\"],[\"甄选推荐双人1套餐\",\"小聚|招牌饮品双人餐\"],[\"【必点】火锅搭子|饮品[双人餐]\",\"小聚|招牌饮品双人餐\"],[\"【工作日专享】港式火锅双人餐\",\"小聚|招牌饮品双人餐\"],[\"【尝鲜】招牌1+1单人餐\",\"小聚|招牌饮品双人餐\"],[\"欢聚实惠4人餐\",\"小聚|招牌饮品双人餐\"],[\"甄选推荐双人1套餐\",\"工作日专享|品尚超值双人餐\"],[\"【必点】火锅搭子|饮品[双人餐]\",\"工作日专享|品尚超值双人餐\"],[\"【工作日专享】港式火锅双人餐\",\"工作日专享|品尚超值双人餐\"],[\"【尝鲜】招牌1+1单人餐\",\"工作日专享|品尚超值双人餐\"],[\"欢聚实惠4人餐\",\"工作日专享|品尚超值双人餐\"]],\"max_seq_len\":64,\"modelIndex\":1,\"modelTypeEnum\":\"TF\",\"model_name\":\"hotpot_deal_name_sim_model\",\"notMatrix\":true,\"pre_handle_struct_type\":0}},\"eventDesc\":\"模型处理结果\",\"req\":{\"cache_type\":0,\"contextData\":{\"cate\":\"火锅\",\"fullLinkTraceId\":\"***********\",\"bizCode\":\"zb_dealMatching\"},\"func_type\":\"bi_convert\",\"groupId\":\"394\",\"input\":[[\"甄选推荐双人1套餐\",\"欢聚实惠四人餐\"],[\"【必点】火锅搭子|饮品[双人餐]\",\"欢聚实惠四人餐\"],[\"【工作日专享】港式火锅双人餐\",\"欢聚实惠四人餐\"],[\"【尝鲜】招牌1+1单人餐\",\"欢聚实惠四人餐\"],[\"欢聚实惠4人餐\",\"欢聚实惠四人餐\"],[\"甄选推荐双人1套餐\",\"尝鲜|招牌1+1单人餐\"],[\"【必点】火锅搭子|饮品[双人餐]\",\"尝鲜|招牌1+1单人餐\"],[\"【工作日专享】港式火锅双人餐\",\"尝鲜|招牌1+1单人餐\"],[\"【尝鲜】招牌1+1单人餐\",\"尝鲜|招牌1+1单人餐\"],[\"欢聚实惠4人餐\",\"尝鲜|招牌1+1单人餐\"],[\"甄选推荐双人1套餐\",\"小聚|招牌饮品双人餐\"],[\"【必点】火锅搭子|饮品[双人餐]\",\"小聚|招牌饮品双人餐\"],[\"【工作日专享】港式火锅双人餐\",\"小聚|招牌饮品双人餐\"],[\"【尝鲜】招牌1+1单人餐\",\"小聚|招牌饮品双人餐\"],[\"欢聚实惠4人餐\",\"小聚|招牌饮品双人餐\"],[\"甄选推荐双人1套餐\",\"工作日专享|品尚超值双人餐\"],[\"【必点】火锅搭子|饮品[双人餐]\",\"工作日专享|品尚超值双人餐\"],[\"【工作日专享】港式火锅双人餐\",\"工作日专享|品尚超值双人餐\"],[\"【尝鲜】招牌1+1单人餐\",\"工作日专享|品尚超值双人餐\"],[\"欢聚实惠4人餐\",\"工作日专享|品尚超值双人餐\"]],\"max_seq_len\":64,\"modelIndex\":1,\"modelTypeEnum\":\"TF\",\"model_name\":\"hotpot_deal_name_sim_model\",\"notMatrix\":true,\"pre_handle_struct_type\":0},\"resp\":\"[{\\\"【尝鲜】招牌1+1单人餐|欢聚实惠四人餐\\\":1.0E-4},{\\\"【必点】火锅搭子|饮品[双人餐]|尝鲜|招牌1+1单人餐\\\":0.3596},{\\\"【工作日专享】港式火锅双人餐|工作日专享|品尚超值双人餐\\\":0.9909},{\\\"【工作日专享】港式火锅双人餐|尝鲜|招牌1+1单人餐\\\":0.001},{\\\"【尝鲜】招牌1+1单人餐|小聚|招牌饮品双人餐\\\":0.0292},{\\\"【必点】火锅搭子|饮品[双人餐]|工作日专享|品尚超值双人餐\\\":0.324},{\\\"【工作日专享】港式火锅双人餐|小聚|招牌饮品双人餐\\\":0.3426},{\\\"【必点】火锅搭子|饮品[双人餐]|欢聚实惠四人餐\\\":4.0E-4},{\\\"欢聚实惠4人餐|工作日专享|品尚超值双人餐\\\":4.0E-4},{\\\"欢聚实惠4人餐|小聚|招牌饮品双人餐\\\":1.0E-4},{\\\"【必点】火锅搭子|饮品[双人餐]|小聚|招牌饮品双人餐\\\":0.9747},{\\\"欢聚实惠4人餐|欢聚实惠四人餐\\\":0.9938},{\\\"欢聚实惠4人餐|尝鲜|招牌1+1单人餐\\\":1.0E-4},{\\\"【尝鲜】招牌1+1单人餐|尝鲜|招牌1+1单人餐\\\":0.9708},{\\\"【尝鲜】招牌1+1单人餐|工作日专享|品尚超值双人餐\\\":6.0E-4},{\\\"【工作日专享】港式火锅双人餐|欢聚实惠四人餐\\\":2.0E-4},{\\\"甄选推荐双人1套餐|小聚|招牌饮品双人餐\\\":0.6755724},{\\\"甄选推荐双人1套餐|欢聚实惠四人餐\\\":7.160477E-5},{\\\"甄选推荐双人1套餐|工作日专享|品尚超值双人餐\\\":0.9136385},{\\\"甄选推荐双人1套餐|尝鲜|招牌1+1单人餐\\\":0.0013824583}]\",\"success\":true,\"traceId\":\"***********\"}";
                PReportMessage message = JSONObject.parseObject(info, PReportMessage.class);
                BmlDataReportUtil.reportData(message);
                return;
            }


            String traceId = predictModelDto.getContextData().get("fullLinkTraceId");
            if (StringUtils.isEmpty(traceId)) {
                traceId = TraceIdUtils.getTraceId(DomainEnum.MATCH);
            }

            PReportMessage message = new PReportMessage(traceId, "模型处理结果");
            message.setReq(predictModelDto);
            message.setResp(JSON.toJSONString(resp));
            message.setBuEnum(BuEnum.DAOCAN);

            Map<String, Object> customData = new HashMap<>();
            customData.put("bizCode", bizCode);
            customData.put("mt_poi_id", predictModelDto.getMt_poi_id());
            customData.put("dj_poi_id", predictModelDto.getDj_poi_id());
            customData.put("mt_deal_id", predictModelDto.getMt_deal_id());
            customData.put("dj_deal_id", predictModelDto.getDj_deal_id());
            message.setCustomData(customData);
            message.setSuccess(success);
            BmlDataReportUtil.reportData(message);
        } catch (Exception e) {
            log.error("上报错误", e);
        }
    }

    public static void reportMatchResult(TPredictRequest req, String respMessage, boolean success) {
        try {
            List<String> dcReportBizCode = Lion.getConfigRepository().getList(LionKeys.DC_NEED_REPORT_BICODE,
                    String.class, new ArrayList<>());
            if (!dcReportBizCode.contains(req.getBizCode())) {
                return;
            }

            if (MdpContextUtils.isOfflineEnv()) {
                String info = "{\"buEnum\":\"DAOCAN\",\"businessId\":\"\",\"customData\":{\"bizCode\":\"zb_dealMatching\",\"dj_poi_id\":\"/59/IfzqfsWc0cI1q9IjkEUI9H2lkCkAkU1lBn0Zq4U=\",\"mt_poi_id\":\"26929\",\"req\":{\"bizCode\":\"zb_dealMatching\",\"extra\":{\"partition_date\":\"2024-08-03 11:00:00\",\"source\":\"udf\",\"traceId\":\"***********\"},\"extraSize\":3,\"req\":{\"mt_deals\":\"[{\\\"mt_deal_id\\\":\\\"***********\\\",\\\"mt_deal_name\\\":\\\"甄选推荐双人1套餐\\\",\\\"mt_market_price\\\":\\\"24000\\\",\\\"mt_price\\\":\\\"13800\\\",\\\"mt_product_type\\\":\\\"1\\\",\\\"mt_deal_max_num\\\":\\\"2\\\",\\\"mt_deal_min_num\\\":\\\"2\\\",\\\"mt_dining_time\\\":\\\"B L S T N \\\",\\\"mt_deal_applicable_group\\\":\\\"0\\\",\\\"mt_dish_set\\\":\\\"乌梅饮||||养生菌王锅||||午餐肉||||手工面||||手打鲜虾丸||||猪肚鸡锅||||生菜||||番茄罗宋锅||||秘制小牛肉||||精品肥牛||||红油麻辣锅||||调料水果||||鳕鱼豆腐\\\",\\\"mt_cnt_set\\\":\\\"2||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||2||||1\\\",\\\"mt_price_set\\\":\\\"900||||1800||||1800||||1200||||3800||||1800||||900||||1600||||2900||||3800||||1600||||800||||2600\\\",\\\"mt_campaign_price\\\":\\\"13800\\\",\\\"mt_poi_id\\\":\\\"26929\\\",\\\"mt_poi_name\\\":\\\"品尚豆捞（工农路店）\\\",\\\"mt_second_cate_name\\\":\\\"火锅\\\",\\\"poi_third_cate_name\\\":\\\"打边炉/港式火锅\\\",\\\"mt_available_date\\\":\\\"节假日||||工作日\\\",\\\"mt_dining_type\\\":\\\"2\\\",\\\"remark\\\":\\\"\\\"}, {\\\"mt_deal_id\\\":\\\"1092392882\\\",\\\"mt_deal_name\\\":\\\"【必点】火锅搭子|饮品[双人餐]\\\",\\\"mt_market_price\\\":\\\"3000\\\",\\\"mt_price\\\":\\\"1680\\\",\\\"mt_product_type\\\":\\\"1\\\",\\\"mt_deal_max_num\\\":\\\"2\\\",\\\"mt_deal_min_num\\\":\\\"2\\\",\\\"mt_dining_time\\\":\\\"B L S T N \\\",\\\"mt_deal_applicable_group\\\":\\\"0\\\",\\\"mt_dish_set\\\":\\\"可与套餐/代金券叠加使用||||手打冻柠茶||||水蜜桃优格乳||||点评收藏打卡送玫瑰布丁||||解腻茉莉冬瓜茶||||鲜芒优格乳\\\",\\\"mt_cnt_set\\\":\\\"1||||1||||1||||1||||1||||1\\\",\\\"mt_price_set\\\":\\\"0||||1200||||1200||||0||||1000||||1500\\\",\\\"mt_campaign_price\\\":\\\"1680\\\",\\\"mt_poi_id\\\":\\\"26929\\\",\\\"mt_poi_name\\\":\\\"品尚豆捞（工农路店）\\\",\\\"mt_second_cate_name\\\":\\\"火锅\\\",\\\"poi_third_cate_name\\\":\\\"打边炉/港式火锅\\\",\\\"mt_available_date\\\":\\\"节假日||||工作日\\\",\\\"mt_dining_type\\\":\\\"2\\\",\\\"remark\\\":\\\"\\\"}, {\\\"mt_deal_id\\\":\\\"1092407901\\\",\\\"mt_deal_name\\\":\\\"【工作日专享】港式火锅双人餐\\\",\\\"mt_market_price\\\":\\\"23700\\\",\\\"mt_price\\\":\\\"15600\\\",\\\"mt_product_type\\\":\\\"1\\\",\\\"mt_deal_max_num\\\":\\\"2\\\",\\\"mt_deal_min_num\\\":\\\"2\\\",\\\"mt_dining_time\\\":\\\"B L S T N \\\",\\\"mt_deal_applicable_group\\\":\\\"0\\\",\\\"mt_dish_set\\\":\\\"七喜||||养生菌王锅||||午餐肉||||品尚鲜虾丸||||土豆片||||嫩滑鸡柳||||手工面||||猪肚鸡锅||||生菜||||番茄罗宋||||百事可乐||||秘制小牛肉||||精品肥牛||||红油麻辣锅||||自助酱料水果\\\",\\\"mt_cnt_set\\\":\\\"1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||2\\\",\\\"mt_price_set\\\":\\\"500||||1800||||1800||||3800||||900||||2200||||1200||||1800||||900||||1600||||500||||2900||||3800||||1600||||800\\\",\\\"mt_campaign_price\\\":\\\"15600\\\",\\\"mt_poi_id\\\":\\\"26929\\\",\\\"mt_poi_name\\\":\\\"品尚豆捞（工农路店）\\\",\\\"mt_second_cate_name\\\":\\\"火锅\\\",\\\"poi_third_cate_name\\\":\\\"打边炉/港式火锅\\\",\\\"mt_available_date\\\":\\\"工作日\\\",\\\"mt_dining_type\\\":\\\"2\\\",\\\"remark\\\":\\\"\\\"}, {\\\"mt_deal_id\\\":\\\"1110592808\\\",\\\"mt_deal_name\\\":\\\"【尝鲜】招牌1+1单人餐\\\",\\\"mt_market_price\\\":\\\"3400\\\",\\\"mt_price\\\":\\\"1800\\\",\\\"mt_product_type\\\":\\\"1\\\",\\\"mt_deal_max_num\\\":\\\"1\\\",\\\"mt_deal_min_num\\\":\\\"1\\\",\\\"mt_dining_time\\\":\\\"L S \\\",\\\"mt_deal_applicable_group\\\":\\\"0\\\",\\\"mt_dish_set\\\":\\\"点评收藏打卡送玫瑰布丁||||熊猫斑斓冻||||葱油鸡蛋飞饼\\\",\\\"mt_cnt_set\\\":\\\"1||||1||||1\\\",\\\"mt_price_set\\\":\\\"0||||1600||||1800\\\",\\\"mt_campaign_price\\\":\\\"1800\\\",\\\"mt_poi_id\\\":\\\"26929\\\",\\\"mt_poi_name\\\":\\\"品尚豆捞（工农路店）\\\",\\\"mt_second_cate_name\\\":\\\"火锅\\\",\\\"poi_third_cate_name\\\":\\\"打边炉/港式火锅\\\",\\\"mt_available_date\\\":\\\"节假日||||工作日\\\",\\\"mt_dining_type\\\":\\\"2\\\",\\\"remark\\\":\\\"\\\"}, {\\\"mt_deal_id\\\":\\\"1120999844\\\",\\\"mt_deal_name\\\":\\\"欢聚实惠4人餐\\\",\\\"mt_market_price\\\":\\\"36200\\\",\\\"mt_price\\\":\\\"26800\\\",\\\"mt_product_type\\\":\\\"1\\\",\\\"mt_deal_max_num\\\":\\\"4\\\",\\\"mt_deal_min_num\\\":\\\"4\\\",\\\"mt_dining_time\\\":\\\"B L S T N \\\",\\\"mt_deal_applicable_group\\\":\\\"0\\\",\\\"mt_dish_set\\\":\\\"乌鸡卷||||养生菌王锅||||冬瓜片||||冻豆腐||||千锤百面||||品尚鲜虾丸||||川味麻辣锅||||深海裙带菜||||牛气冲天||||猪肚鸡锅||||番茄罗宋锅||||精品肥牛||||经典矿泉锅||||罗马生菜||||羔羊肉卷||||调料水果||||酸辣红汤锅||||龙口粉丝\\\",\\\"mt_cnt_set\\\":\\\"1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||4||||1||||1\\\",\\\"mt_price_set\\\":\\\"2200||||1800||||600||||900||||1200||||3800||||1600||||1000||||6800||||1800||||1600||||3800||||800||||900||||3800||||800||||1200||||800\\\",\\\"mt_campaign_price\\\":\\\"26800\\\",\\\"mt_poi_id\\\":\\\"26929\\\",\\\"mt_poi_name\\\":\\\"品尚豆捞（工农路店）\\\",\\\"mt_second_cate_name\\\":\\\"火锅\\\",\\\"poi_third_cate_name\\\":\\\"打边炉/港式火锅\\\",\\\"mt_available_date\\\":\\\"节假日||||工作日\\\",\\\"mt_dining_type\\\":\\\"2\\\",\\\"remark\\\":\\\"\\\"}]\",\"dj_deals\":\"[{\\\"dj_deal_id\\\":\\\"NWiL0neVYPiArWZ28CiBIm+o/rPlYDpz2zjU2QltzOk=\\\",\\\"dj_deal_name\\\":\\\"欢聚实惠四人餐\\\",\\\"dj_market_price\\\":\\\"35800\\\",\\\"dj_price\\\":\\\"26800\\\",\\\"dj_product_type\\\":\\\"1\\\",\\\"dj_dining_time\\\":\\\"B L S T N\\\",\\\"dj_dining_date\\\":\\\"\\\",\\\"dj_deal_max_num\\\":\\\"4\\\",\\\"dj_deal_min_num\\\":\\\"4\\\",\\\"dj_deal_applicable_group\\\":\\\"0\\\",\\\"dj_dish_set\\\":\\\"乌鸡卷||||冬瓜片||||冻豆腐||||千锤百面||||品尚鲜虾丸||||川香麻辣锅||||深海裙带菜||||牛气冲天||||番茄罗宋锅||||矿泉水锅||||精品肥牛||||罗马生菜||||羔羊肉卷||||胡椒猪肚鸡锅||||自助酱料水果||||菌王锅||||酸辣红汤锅||||龙口粉丝\\\",\\\"dj_cnt_set\\\":\\\"1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||4||||1||||1||||1\\\",\\\"dj_price_set\\\":\\\"2200||||600||||900||||1200||||3800||||1600||||1000||||6800||||1600||||800||||3800||||900||||3800||||1800||||800||||1800||||1200||||800\\\",\\\"dj_campaign_price\\\":\\\"26800\\\",\\\"mt_poi_id\\\":\\\"26929\\\",\\\"mt_second_cate_name\\\":\\\"火锅\\\",\\\"poi_third_cate_name\\\":\\\"打边炉/港式火锅\\\",\\\"mt_management_type\\\":\\\"8\\\",\\\"mt_brand_name\\\":\\\"品尚豆捞\\\",\\\"dj_poi_id\\\":\\\"/59/IfzqfsWc0cI1q9IjkEUI9H2lkCkAkU1lBn0Zq4U=\\\",\\\"dj_available_date\\\":\\\"节假日||||工作日\\\",\\\"dj_dining_type\\\":\\\"2\\\",\\\"pic_url\\\":\\\"\\\"}, {\\\"dj_deal_id\\\":\\\"cBb5ZQNT5Hv95hY2pvkCoSKObWMXukoQZUT/jGFpZ6c=\\\",\\\"dj_deal_name\\\":\\\"尝鲜|招牌1+1单人餐\\\",\\\"dj_market_price\\\":\\\"3401\\\",\\\"dj_price\\\":\\\"1200\\\",\\\"dj_product_type\\\":\\\"1\\\",\\\"dj_dining_time\\\":\\\"S \\\",\\\"dj_dining_date\\\":\\\"\\\",\\\"dj_deal_max_num\\\":\\\"1\\\",\\\"dj_deal_min_num\\\":\\\"1\\\",\\\"dj_deal_applicable_group\\\":\\\"0\\\",\\\"dj_dish_set\\\":\\\"可与套餐代金券叠加使用||||熊猫斑斓奶冻||||葱油鸡蛋飞饼\\\",\\\"dj_cnt_set\\\":\\\"1||||1||||1\\\",\\\"dj_price_set\\\":\\\"1||||1600||||1800\\\",\\\"dj_campaign_price\\\":\\\"1200\\\",\\\"mt_poi_id\\\":\\\"26929\\\",\\\"mt_second_cate_name\\\":\\\"火锅\\\",\\\"poi_third_cate_name\\\":\\\"打边炉/港式火锅\\\",\\\"mt_management_type\\\":\\\"8\\\",\\\"mt_brand_name\\\":\\\"品尚豆捞\\\",\\\"dj_poi_id\\\":\\\"/59/IfzqfsWc0cI1q9IjkEUI9H2lkCkAkU1lBn0Zq4U=\\\",\\\"dj_available_date\\\":\\\"节假日||||工作日\\\",\\\"dj_dining_type\\\":\\\"2\\\",\\\"pic_url\\\":\\\"14dfbce7a16d7a51edff4c00d638c07d,4ed4bcc521c7d7cce55bf82d249dbe9f\\\"}, {\\\"dj_deal_id\\\":\\\"k8nnXhkmx87LHH9c4DtOJsRArAK5neD05A1fFdHbmwA=\\\",\\\"dj_deal_name\\\":\\\"小聚|招牌饮品双人餐\\\",\\\"dj_market_price\\\":\\\"3000\\\",\\\"dj_price\\\":\\\"1180\\\",\\\"dj_product_type\\\":\\\"1\\\",\\\"dj_dining_time\\\":\\\"B L S T N\\\",\\\"dj_dining_date\\\":\\\"\\\",\\\"dj_deal_max_num\\\":\\\"2\\\",\\\"dj_deal_min_num\\\":\\\"2\\\",\\\"dj_deal_applicable_group\\\":\\\"0\\\",\\\"dj_dish_set\\\":\\\"手打冻柠茶||||水蜜桃优格乳||||解腻茉莉冬瓜茶||||鲜芒优格乳\\\",\\\"dj_cnt_set\\\":\\\"1||||1||||1||||1\\\",\\\"dj_price_set\\\":\\\"1200||||1200||||1000||||1500\\\",\\\"dj_campaign_price\\\":\\\"1180\\\",\\\"mt_poi_id\\\":\\\"26929\\\",\\\"mt_second_cate_name\\\":\\\"火锅\\\",\\\"poi_third_cate_name\\\":\\\"打边炉/港式火锅\\\",\\\"mt_management_type\\\":\\\"8\\\",\\\"mt_brand_name\\\":\\\"品尚豆捞\\\",\\\"dj_poi_id\\\":\\\"/59/IfzqfsWc0cI1q9IjkEUI9H2lkCkAkU1lBn0Zq4U=\\\",\\\"dj_available_date\\\":\\\"节假日||||工作日\\\",\\\"dj_dining_type\\\":\\\"2\\\",\\\"pic_url\\\":\\\"\\\"}, {\\\"dj_deal_id\\\":\\\"tPzXUemJxDLUBAY8bhymc9cfJFrSQN0aHcp61PeCmxc=\\\",\\\"dj_deal_name\\\":\\\"工作日专享|品尚超值双人餐\\\",\\\"dj_market_price\\\":\\\"23700\\\",\\\"dj_price\\\":\\\"15600\\\",\\\"dj_product_type\\\":\\\"1\\\",\\\"dj_dining_time\\\":\\\"B L S T N\\\",\\\"dj_dining_date\\\":\\\"1 \\\",\\\"dj_deal_max_num\\\":\\\"2\\\",\\\"dj_deal_min_num\\\":\\\"2\\\",\\\"dj_deal_applicable_group\\\":\\\"0\\\",\\\"dj_dish_set\\\":\\\"七喜||||养生菌王锅||||午餐肉||||品尚鲜虾丸||||土豆片||||嫩滑鸡柳||||手工面||||猪肚鸡锅||||生菜||||番茄罗宋锅||||百事可乐||||秘制小牛肉||||精品肥牛||||红油麻辣锅||||自助酱料水果\\\",\\\"dj_cnt_set\\\":\\\"1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||1||||2\\\",\\\"dj_price_set\\\":\\\"500||||1800||||1800||||3800||||900||||2200||||1200||||1800||||900||||1600||||500||||2900||||3800||||1600||||800\\\",\\\"dj_campaign_price\\\":\\\"15600\\\",\\\"mt_poi_id\\\":\\\"26929\\\",\\\"mt_second_cate_name\\\":\\\"火锅\\\",\\\"poi_third_cate_name\\\":\\\"打边炉/港式火锅\\\",\\\"mt_management_type\\\":\\\"8\\\",\\\"mt_brand_name\\\":\\\"品尚豆捞\\\",\\\"dj_poi_id\\\":\\\"/59/IfzqfsWc0cI1q9IjkEUI9H2lkCkAkU1lBn0Zq4U=\\\",\\\"dj_available_date\\\":\\\"工作日\\\",\\\"dj_dining_type\\\":\\\"2\\\",\\\"pic_url\\\":\\\"3b5cad39737ba77d994c6b57ad4e9519,4deaabc122955d96d18f6c15680d9989,7898c2cd254259b9b4d3b8f39d85a56e,8b08490d31c33407ee4e236823e3bcf7,bff52f336cd77cfb676acbb02a1f118f,ca142ae95381325160492ed94c1d6553,e7be09f224585c24f87c6f43d4007031\\\"}]\",\"source\":\"udf\"},\"reqSize\":3,\"setAbtestKey\":false,\"setBizCode\":true,\"setExtra\":true,\"setReq\":true}},\"djPoiId\":\"/59/IfzqfsWc0cI1q9IjkEUI9H2lkCkAkU1lBn0Zq4U=\",\"eventDesc\":\"匹配结果\",\"mtPoiId\":\"26929\",\"req\":{\"$ref\":\"$.customData.req\"},\"resp\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"dj_full_match\\\":\\\"[]\\\",\\\"code\\\":\\\"0\\\",\\\"match\\\":\\\"\\\",\\\"compressed_match\\\":\\\"H4sIAAAAAAAAANWX+1MTVxTH/5f8Wkb2vXuZ8YcAtYCURzVF6HQyYbOUJdkEksUirTMFDJgQiEgKolFAQAs4JYDCQiD+M9nXf9G7Nw+SLKlax6lsZna4d+/jnHvO53sPP/3mkGS3VwwPusOC7Ghw5E7iprKuzh3/Dh81ktGTq0Yirs/PmskI6pp+oy+m9OhMvpEx9qeNyCPUmJsyk5v6StZ8/hK1FyfU9FPzIGWsZHOKgrqOn6sLh+ZpxshuFNbTjve1d8fGxoqqvDISKatLjz7T0kl1+kDd3Ebt+I4x+RRaVZii/7ljxB/o58vq30Wr9IMs3M6Y3IJzUTuTUCOKvvpeO4wWh5wvm9tvkDv5XbJJPRszJqMFX430lLa0oqXfai/QdzOiQCv105fa/mZhCTO7oiY29INoTnnuqHMEPJLgHvLwvCfkdYdFydGAXWPpOodXDIiBX9xejywUu3E4OugOCWE5JPKyWwy7JY/MDzoa5NCoUOeobqOV/Xy4OJ1mcYrgLt51VtDCAh8MeN28tY01AQZPn9iFpkLTvENuz12P6Pf0+wVkCPxoxCbViRlteSsfia3ceQo24GBJ8Pjd8r1hOIiAU61WYFTK741fw0oOyaIklPVCE4aDolv0wrUJBhDAWsoT8gmwH3pZ8h1wHKAxgmUIisQAtD3MB0OC9YXDCAbZivZEC3X0iO1YQPixt0t0hnr6CK5JbGyVvgnWh7r8vc3D48T4kIvo9svjnb7rlp8ocfP7cBxHsDiDUYC0/qCLX6E9xWNkaAJnSYCTGEVSLId8+PhzsryVB8VQ5Zlr0UUje6JPRus1ZU89S5RiYEVHlMVgIAz3Rm6WjqueBvWtA+MjA+EeHuNb8RHQOuT71tUKWgi/r8nn9Llwf2MA6xuhXNfz0axJ6GfiaL5a+WQcL8EPgQbZqYXjh9ibWtc2H1dzbszsqLFtM7JfwWWFGn0YUkvdStmF4wQGAOAoKp87fHA0IF+epyR3v65aG/XJE2PvgZY8MpYTMOzq3Lr5x4Q+v6svpFVlKqdMIN+mY3p8V1ta0JYWYUCQyQv72sEJdMx4NmtuvDC3zmqoB8AqfviX0BKWqPiRV0tLAGAxGjAcR8JhTLmYANomJnxjP93X3XGbbrkL6MFeYviuryl462Znf8/3d0Z9we4+1+36oe9uDPcxfLWYAJwpf1i7mBBXUz3UxF5OmVe3ls3NR7nMhjmzoD5U1MSaGlvLnb/Xk3/Zcnjr6N/SuIovHEaH4DDuEr7gMVIsxlA0FGqKoQg7X+W21dc0TovOWvBNZ7TVNSN+hLrSb41USlufyp090dbOcieHyIuPwdV4vWFEMkY8asxH82JaWNNSytjj0oI1iCW/zIVP2dLvykCKhpaopOCdW0WljwsE7gz6pDGObW9pATzVLHe2hX9whpw36YDQjNFOfOCGt6Vf+tVpu+JJruKxU8lUPVcT0o9O8U/I3kpQMUCQEFSOuARUFiNIDoN6WnjbQc0pU+pSqtZ/CHMPIb/wcq9VdaRWYZVSLFl2t7XMAtQUbfWwADeMR6FksRX+FwVEWQ2C2nD101lLP05Q8aO/fqI+PFLTCViAFCyx1SSwaIBqVl782AuOGtRDHYMqRpA0ASgcZ617yKYBiIPP0QASqiROX7z/W9X/P3EPMAav4l7uGr/jEqS2seZ2V6Ozl+sfvCfxgB9ouxG61d2BeVr4YQbvEpqkMfttzH3oNuYsHb4M9a+Q7q+KnuLHLweQTXgojAUYbhMemED3f/4H66Rq05AQAAA=\\\"},\\\"dataSize\\\":4,\\\"extra\\\":{\\\"cost\\\":\\\"15\\\",\\\"cacheKey\\\":\\\"zb_dealMatching_1_7_4ede4d2c553dddd3bd7a446ee1864629_staging\\\",\\\"cacheVersion\\\":\\\"6e51310d329\\\",\\\"useCache\\\":\\\"true\\\",\\\"unique_key\\\":\\\"4ede4d2c553dddd3bd7a446ee1864629\\\",\\\"strategy\\\":\\\"deal_match\\\",\\\"distribution\\\":\\\"group_a\\\"},\\\"extraSize\\\":7,\\\"setCode\\\":true,\\\"setData\\\":true,\\\"setExtra\\\":true,\\\"setMessage\\\":false}\",\"success\":true,\"traceId\":\"***********\"}";
                PReportMessage message = JSONObject.parseObject(info, PReportMessage.class);
                BmlDataReportUtil.reportData(message);
                return;
            }

            String traceId = "", businessId = "";
            Map<String, String> reqExtra = req.getExtra();
            if (MapUtils.isNotEmpty(reqExtra)) {
                traceId = reqExtra.get("traceId");
                if (StringUtils.isEmpty(traceId)) {
                    traceId = TraceIdUtils.getTraceId(DomainEnum.MATCH);
                }
                businessId = reqExtra.getOrDefault("businessId", "");
            }

            PReportMessage message = new PReportMessage(traceId, "匹配结果");
            message.setReq(req);
            message.setResp(respMessage);

            message.setBusinessId(businessId);
            message.setBuEnum(BuEnum.DAOCAN);


            Map<String, Object> customData = new HashMap<>();
            customData.put("bizCode", req.getBizCode());


            String iptDealsJson = req.getReq().get("ipt_deals");
            if (StringUtils.isNotEmpty(iptDealsJson)) {
                customData.put("data_source", reqExtra.get("data_source"));
                customData.put("dp_brand_id", getPoiId(iptDealsJson, "dp_brand_id"));
                String mtPoiId = getPoiId(iptDealsJson, "mt_poi_id");
                String djPoiId = getPoiId(iptDealsJson, "dj_poi_id");
                customData.put("mt_poi_id", mtPoiId);
                customData.put("dj_poi_id", djPoiId);
                message.setMtPoiId(mtPoiId);
                message.setDjPoiId(djPoiId);
            }
            String mtDealsJson = req.getReq().get("mt_deals");
            if (StringUtils.isNotEmpty(mtDealsJson)) {
                String djDealsJson = req.getReq().get("dj_deals");
                String mtPoiId = getPoiId(mtDealsJson, "mt_poi_id");
                String djPoiId = getPoiId(djDealsJson, "dj_poi_id");
                customData.put("mt_poi_id", mtPoiId);
                customData.put("dj_poi_id", djPoiId);
                message.setMtPoiId(mtPoiId);
                message.setDjPoiId(djPoiId);
            }
            message.setCustomData(customData);
            message.setSuccess(success);
            BmlDataReportUtil.reportData(message);
        } catch (Exception e) {
            log.error("上报错误", e);
        }
    }

    private static String getPoiId(String json, String key) {
        if (StringUtils.isNotBlank(json)) {
            JSONArray jsonArray = JSON.parseArray(json);
            if (!jsonArray.isEmpty()) {
                JSONObject mtDeal = jsonArray.getJSONObject(0);
                return mtDeal.getString(key);
            }
        }
        return null;
    }
}
