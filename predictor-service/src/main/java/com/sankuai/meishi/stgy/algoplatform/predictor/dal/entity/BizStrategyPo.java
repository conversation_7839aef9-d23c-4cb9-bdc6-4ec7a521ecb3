package com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity;

import lombok.*;

import java.util.Date;

/**
 * 表名: biz_strategy
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BizStrategyPo {
    /**
     * 字段: id
     * 说明: 自增主键
     */
    private Long id;

    /**
     * 字段: biz_code
     * 说明: 适用业务标识
     */
    private String bizCode;

    /**
     * 字段: note
     * 说明: 业务策略说明
     */
    private String note;

    /**
     * 字段: abtest_config
     * 说明: 算法包说明
     */
    private String abtestConfig;

    /**
     * 字段: status
     * 说明: 状态，0：正常，-1：删除
     */
    private Integer status;

    /**
     * 字段: add_time
     * 说明: 添加时间
     */
    private Date addTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    private Date updateTime;
}