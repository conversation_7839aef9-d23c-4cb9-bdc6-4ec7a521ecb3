package com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice;

import com.google.common.base.Preconditions;
import com.meituan.mdp.boot.starter.mdpcache.anno.Cached;
import com.meituan.mdp.boot.starter.mdpcache.core.CacheType;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.PredictionDataScriptExample;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.PredictionDataScriptMapper;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class PredictionDataScriptDao {
    @Resource
    private PredictionDataScriptMapper scriptMapper;

    @CatTransaction(type = "DaoService", name = "PredictionDataScriptDao.getValidByCodeWithCache")
    @Cached(area = "a1", key = "#bizCode", cacheType = CacheType.LOCAL, timeUnit = TimeUnit.SECONDS, expire = 10, localLimit = 1000)
    public PredictionDataScriptPo getValidByCodeWithCache(String bizCode) {
        PredictionDataScriptExample example = new PredictionDataScriptExample();
        example.createCriteria().andStatusEqualTo(0)
                .andBizCodeEqualTo(bizCode);
        List<PredictionDataScriptPo> pos = scriptMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        Preconditions.checkState(pos.size() == 1, "getValidByCodeWithCache 不唯一: %s", bizCode, pos);
        return pos.get(0);
    }

    public List<PredictionDataScriptPo> getValidByCode(List<String> bizCodes) {
        if (CollectionUtils.isEmpty(bizCodes)) {
            return Collections.emptyList();
        }
        PredictionDataScriptExample example = new PredictionDataScriptExample();
        PredictionDataScriptExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(0);
        criteria.andBizCodeIn(bizCodes);
        List<PredictionDataScriptPo> pos = scriptMapper.selectByExample(example);
        return Optional.ofNullable(pos).orElseGet(ArrayList::new);
    }
}
