package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import com.google.common.collect.EvictingQueue;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreter;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreterFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class PythonMonitorService {
    @Resource
    @Qualifier("pythonRuntimeStatus")
    private String pythonRuntimeStatus;

    /**
     * 记录3分钟内的心跳状态
     */
    private final int interpreterStatusWindowSize = 3 * 60;
    private Map<String, EvictingQueue<Boolean>> statusQueue = new ConcurrentHashMap<>();

    @Scheduled(cron = "* * * * * ?")
    public void reportHeartbeat() {
        List<PythonInterpreter> interpreters = PythonInterpreterFactory.getAll();
        for (PythonInterpreter interpreter : interpreters) {

            boolean h = false;
            String processName = interpreter.getProcessName();
            try {
                h = RaptorTrack.take("PythonMonitor_Heartbeat", processName, () -> {
                    boolean heartbeat = interpreter.heartbeat();
                    Preconditions.checkState(heartbeat, "心跳检测状态不正确:%s", processName);
                    return heartbeat;
                });
            } catch (Exception e) {
                log.error("interpreter reportHeartbeat error: {}, ex:", processName, e);
            }
            boolean finalH = h;
            statusQueue.compute(processName, (k, oldVal) -> {
                EvictingQueue<Boolean> newVal = oldVal;
                if (newVal == null) {
                    newVal = EvictingQueue.create(interpreterStatusWindowSize);
                    for (int i = 0; i < interpreterStatusWindowSize; i++) {
                        newVal.add(true);
                    }
                }
                newVal.add(finalH);
                return newVal;
            });
        }
    }

    @Scheduled(cron = "0 * * * * ?")
    public void checkAndRecover() {
        if (!Lion.getConfigRepository().getBooleanValue(LionKeys.PYTHON_AUTO_RECOVER_SWITCH, true)) {
            log.info("checkAndRecover disabled.");
            return;
        }
        log.info("checkAndRecover checking.");
        statusQueue.forEach((processName, queue) -> {
            try {
                if (queue.stream().noneMatch(f -> f)) {
                    log.warn("checkAndRecover dead process: {}", processName);
                    RaptorTrack.Python_DeadProcess.report(processName);
                    PythonInterpreter interpreter = PythonInterpreterFactory.getRuntimeByProcessName(processName);
                    if (interpreter != null) {
                        interpreter.restart();
                    }
                }
            } catch (Exception e) {
                log.error("checkAndRecover error: name:{}, ex:", processName, e);
            }
        });
    }

}
