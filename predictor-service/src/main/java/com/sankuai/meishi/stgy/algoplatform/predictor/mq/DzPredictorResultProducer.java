package com.sankuai.meishi.stgy.algoplatform.predictor.mq;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024年03月29日 10:30 上午
 */
@Service
@Slf4j
public class DzPredictorResultProducer {

    @Autowired
    @Qualifier("dzPredictorResult")
    private IProducerProcessor producer;

   /*
   若未配置 [producerName] ，Bean 的名称会根据producer配置自动拼接，拼接格式为：[bgNameSpace] + [appkey] + [topicName]
   @Autowired
   @Qualifier("daozong#com.sankuai.algoplatform.predictor#dz.algoplatform.predictor.predict.result")
   private static IProducerProcessor producer;
   */

    public void send(String msg) throws Exception {
        ProducerResult producerResult = producer.sendMessage(msg);
    }
}
