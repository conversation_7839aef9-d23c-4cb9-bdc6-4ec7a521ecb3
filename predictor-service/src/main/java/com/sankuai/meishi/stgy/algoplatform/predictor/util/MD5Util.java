package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2023/9/7 11:50
 **/
@Data
@Slf4j
public class MD5Util {


    public static String md5(String input) {
        // 创建MessageDigest对象，指定使用MD5算法
        MessageDigest md = null;
        String md5Hash = null;
        try {
            md = MessageDigest.getInstance("MD5");
            // 将输入数据转换为字节数组
            byte[] inputBytes = input.getBytes();
            // 计算MD5哈希值
            byte[] hashBytes = md.digest(inputBytes);
            // 将字节数组转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            md5Hash = sb.toString();
            return md5Hash;
        } catch (Exception e) {
            log.error("MD5Util error,input={},message={}", input, e.getMessage(), e);
            return null;
        }
    }


    public static void main(String[] args) {
        String s = md5("[\"手擀面\",\"手工面\"]");
//        System.out.println(s);
    }
}
