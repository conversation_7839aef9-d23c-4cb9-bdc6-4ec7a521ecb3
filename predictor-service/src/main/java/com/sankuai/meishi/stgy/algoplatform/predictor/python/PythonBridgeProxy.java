package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.Lion;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.RateLimiter;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ClassUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import py4j.GatewayServer;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.net.InetAddress;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

@Slf4j
public class PythonBridgeProxy implements InvocationHandler {
    private final static AtomicInteger runningTaskNum = new AtomicInteger(0);
    private final static ScheduledFuture<?> scheduledFuture = Executors.newScheduledThreadPool(1)
            .scheduleAtFixedRate(() -> {
                try {
                    String host = InetAddress.getLocalHost().getHostAddress();
                    Cat.logMetricForCount("BridgeRunningTaskCnt", runningTaskNum.get(), ImmutableMap.of("host", host));
                } catch (Exception e) {
                    log.error("BridgeProxy TaskMonitor error:", e);
                }
            }, 1, 1, TimeUnit.SECONDS);
    private volatile static RateLimiter rateLimiter = RateLimiter.create(10000);

    static {
        Consumer<Integer> listener = val -> {
            if (val != null && val > 0) {
                rateLimiter = RateLimiter.create(val);
                log.info("rateLimiter rate change2: {}", val);
            }
        };
        Lion.getConfigRepository()
                .addConfigListener(LionKeys.BRIDGE_RATE_LIMIT, e -> listener.accept(Integer.valueOf(e.getValue())));
        listener.accept(Lion.getConfigRepository().getIntValue(LionKeys.BRIDGE_RATE_LIMIT, 10000));
    }

    public static int getRunningTaskNum() {
        return runningTaskNum.get();
    }

    /**
     * PythonBridgeProxy - Cache
     */
    private static final Cache<String, PythonBridge> proxyCache = CacheBuilder.newBuilder()
            .maximumSize(10)
            .build();

    public static PythonBridge getProxyInstance(String id, GatewayServer server) {
        try {
            return proxyCache.get(id, () -> {
                return (PythonBridge) Proxy.newProxyInstance(PythonBridgeProxy.class.getClassLoader(), new Class[]{PythonBridge.class},
                        new PythonBridgeProxy(id, server));
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    // --------------------------------------------------------------------------------
    private String id;
    private GatewayServer gatewayServer;

    public PythonBridgeProxy(String id, GatewayServer gatewayServer) {
        this.id = id;
        this.gatewayServer = gatewayServer;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        rateLimiter.acquire();
        long t0 = System.currentTimeMillis();
        String classPathName = method.getDeclaringClass().getName();
        String methodName = method.getName();
        String fullPath = this.id + "|" + classPathName + "#" + methodName;
        Transaction t = Cat.newTransaction("PythonBridge", fullPath);
        Object result;
        try {
            runningTaskNum.incrementAndGet();

            Object entryPoint = gatewayServer.getPythonServerEntryPoint(new Class[]{ClassUtils.getClass(classPathName)});
            result = MethodUtils.invokeMethod(entryPoint, methodName, args);
            t.setStatus(Event.SUCCESS);
            return result;
        } catch (Exception e) {
            t.setStatus(e);
            throw e;
        } finally {
            log.info("PythonBridgeProxy.invoke {}, cost:{}", fullPath, System.currentTimeMillis() - t0);
            t.complete();
            runningTaskNum.decrementAndGet();
        }
    }
}
