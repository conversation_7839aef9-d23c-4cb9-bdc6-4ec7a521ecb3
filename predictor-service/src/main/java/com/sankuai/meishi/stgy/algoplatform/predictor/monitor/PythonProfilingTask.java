package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.sankuai.meishi.stgy.algoplatform.predictor.python.util.LocationUtil.getRuntimePath;

@Data
@Slf4j
public class PythonProfilingTask {
    private String processId;
    private String processName;
    private String submitTime;
    private int duration;
    private String finishTime;
    private String outputId;
    private String outputMsg;
    @J<PERSON>NField(serialize = false)
    private volatile Process process;

    public PythonProfilingTask(String outputMsg) {
        this.outputMsg = outputMsg;
    }

    public PythonProfilingTask(PythonInterpreter interpreter, int duration) {
        this.processName = interpreter.getName();
        this.processId = interpreter.getPrecessId();
        this.submitTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        this.duration = duration;
        this.finishTime = LocalDateTime.now().plusSeconds(duration).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        this.outputId = String.format("%s_%s", this.processName, System.currentTimeMillis());
    }

    public void start() {
        String runtimePath = getRuntimePath() + "/python/" + this.processName + "/bin";
        List<String> outputs = new ArrayList<>();
        try {
            File dir = new File(System.getProperty("java.io.tmpdir") + File.separator + "PythonProfiling" + File.separator);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String tmpFile = dir.getAbsolutePath() + File.separator + this.outputId + ".svg";
            this.process = Runtime.getRuntime().exec(String.format("./py-spy record -o %s --pid %s --duration %s", tmpFile, this.processId, this.duration),
                    null, new File(runtimePath));
            this.process.waitFor();

            outputs.addAll(IOUtils.readLines(new InputStreamReader(this.process.getInputStream())));
            outputs.addAll(IOUtils.readLines(new InputStreamReader(this.process.getErrorStream())));
        } catch (InterruptedException | IOException e) {
            log.warn("PythonProfilingTask.start, e:", e);
            outputs.add(e.getMessage());
        }
        this.outputMsg += String.join("\n", outputs);
        try {
            Thread.sleep(1000 * 60);
        } catch (InterruptedException e) {
            log.warn("start wait", e);
        }
    }

    public boolean isRunning() {
        return Optional.ofNullable(this.process).map(Process::isAlive).orElse(false);
    }
}