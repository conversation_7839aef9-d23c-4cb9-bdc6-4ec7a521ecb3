package com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice;

import com.google.common.base.Preconditions;
import com.meituan.mdp.boot.starter.mdpcache.anno.Cached;
import com.meituan.mdp.boot.starter.mdpcache.core.CacheType;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BizStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.BizStrategyExample;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.BizStrategyMapper;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class BizStrategyDao {
    @Resource
    private BizStrategyMapper bizStrategyMapper;

    @CatTransaction(type = "DaoService", name = "BizStrategyDao.getValidByCodeWithCache")
    @Cached(area = "a1", key = "#bizCode", cacheType = CacheType.LOCAL, timeUnit = TimeUnit.SECONDS, expire = 10, localLimit = 1000)
    public BizStrategyPo getValidByCodeWithCache(String bizCode) {
        BizStrategyExample example = new BizStrategyExample();
        example.createCriteria().andStatusEqualTo(0)
                .andBizCodeEqualTo(bizCode);
        List<BizStrategyPo> pos = bizStrategyMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        Preconditions.checkState(pos.size() == 1, "getValidByCodeWithCache 不唯一: %s", bizCode, pos);
        return pos.get(0);
    }
}
