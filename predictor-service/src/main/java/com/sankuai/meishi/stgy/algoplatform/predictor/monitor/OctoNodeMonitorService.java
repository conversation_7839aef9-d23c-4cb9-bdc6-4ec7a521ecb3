package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.HttpUtil;
import com.sankuai.oceanus.http.internal.HttpHeader;
import com.sankuai.oceanus.http.internal.TargetRequestEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.sankuai.meishi.stgy.algoplatform.predictor.util.HttpUtil.OCTO_APP_KEY;

@Component
@Slf4j
public class OctoNodeMonitorService {

    @MdpConfig(LionKeys.OCTO_ALIVE_NODES_THRESHOLD)
    String configStr;

    public void serverNodeMonitor() {

        Map<String, List<Map<String, String>>> config =
                JSONObject.parseObject(configStr, new TypeReference<Map<String, List<Map<String, String>>>>() {
                });

        for (Map.Entry<String, List<Map<String, String>>> entry : config.entrySet()) {
            checkServerNodes(entry.getKey(), entry.getValue());
        }

    }

    private void checkServerNodes(String appKey, List<Map<String, String>> cellList) {
        Map<String, Integer> cellThresholdConfigMap = new HashMap<>();
        for (Map<String, String> map : cellList) {
            String threshold = map.get("threshold");
            String cell = map.get("cell");
            if (Integer.parseInt(threshold) <= 2) {
                RaptorTrack.Sys_UnexpectedVisitNum.report("AliveNodeConfigError#" + appKey + "_" + cell);
                continue;
            }
            cellThresholdConfigMap.put(cell, Integer.parseInt(threshold));
        }


//       接口文档：https://km.sankuai.com/collabpage/**********#id-%E4%B8%80%E3%80%81GET%E6%8E%A5%E5%8F%A3
        String url = "https://octoopenapi.vip.sankuai.com/v1/provider";
        Map<String, String> param = new HashMap<String, String>() {
            {
                put("appkey", appKey);
                put("type", "1");
//                put("env", "4"); //1: dev; 2: test; 3: stage; 4: prod。默认4PROD
                put("status", "2");//节点状态：默认-1 -1 :所有 0:未启动 1: 启动中 2:正常 4:禁用
//                put("pageNo", "1");
//                put("pageSize", "100");
            }
        };

        JSONObject obj = getNodeInfo(url, param);
        if (obj == null || obj.getBoolean("success") == null || !obj.getBoolean("success")) {
            log.error("OctoNodeMonitorService.serverNodeMonitor http request error.");
            return;
        }

        Map<String, Integer> aliveServerCntMap = new ConcurrentHashMap<>();
        getCellNodeCnt(aliveServerCntMap, obj);

        Map<String, Integer> page = obj.getJSONObject("data").getObject("page", new TypeReference<Map<String, Integer>>() {
        });
        Integer totalPageCount = page.get("totalPageCount");
        for (int pageNo = 2; pageNo <= totalPageCount; pageNo++) {
            param.put("pageNo", String.valueOf(pageNo));
            JSONObject nodeInfo = getNodeInfo(url, param);
            if (nodeInfo != null) {
                getCellNodeCnt(aliveServerCntMap, nodeInfo);
            }
        }


        cellThresholdConfigMap.forEach((cell, threshold) -> {
            int aliveServerCnt = aliveServerCntMap.get(cell);
            if (aliveServerCnt < threshold) {
                log.error("{}#{} current alive node num:{}", appKey, cell, aliveServerCnt);
                Cat.logEvent("InsufficientAliveNode#" + appKey, String.valueOf(aliveServerCnt));
            }
        });
    }

    private JSONObject getNodeInfo(String url, Map<String, String> param) {
        Map<String, String> header = ImmutableMap.of(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, OCTO_APP_KEY,
                HttpHeader.OCEANUS_TARGET_ENV_HEADER, TargetRequestEnv.ONLINE.getDesc());
        String respStr = HttpUtil.httpGet(url, param, header);
        if (StringUtils.isEmpty(respStr)) {
            return null;
        }
        JSONObject object = JSON.parseObject(respStr);
        boolean isSuccess = object.getBoolean("success");
        if (!isSuccess) {
            RaptorTrack.Sys_UnexpectedVisitNum.report("OctoNodeReqError");
            return null;
        }
        return JSON.parseObject(respStr);
    }

    private void getCellNodeCnt(Map<String, Integer> aliveServerCntMap, JSONObject obj) {
        List<Map<String, Object>> data = obj.getJSONObject("data").getObject("providers", new TypeReference<List<Map<String, Object>>>() {
        });
        for (Map<String, Object> datum : data) {
            String actCell = (String) datum.get("cell");
            if ("".equals(actCell)) {
                actCell = "default";
            }
            aliveServerCntMap.put(actCell, aliveServerCntMap.getOrDefault(actCell, 0) + 1);
        }
    }
}
