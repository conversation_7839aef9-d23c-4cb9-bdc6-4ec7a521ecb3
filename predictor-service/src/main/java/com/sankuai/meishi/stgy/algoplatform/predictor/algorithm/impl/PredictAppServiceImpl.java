package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mtrace.ServerTracer;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.*;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.PredictCacheConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.PredictCacheContent;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoStrategyDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.BizStrategyDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BizStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.CompressUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.LogTools;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.MDC;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants.MODEL_ERROR_POI_CACHE;
import static com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants.PREDICT_CACHE_KEY_FORMAT;
import static com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack.PREDICT_CACHE_NUM;
import static com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack.Predict_SlowRequest;

@Slf4j
@Service
public class PredictAppServiceImpl implements PredictAppService {
    @Resource
    private BizStrategyDao bizStrategyDao;
    @Resource
    private AlgoStrategyDao algoStrategyDao;
    @Resource
    private AlgoPackageService algoPackageService;

    @Autowired
    private TairClient tairClient;

    @Override
    public PredictContext predict(PredictContext context) {
        return RaptorTrack.take("PredictAppService", context.getBizCode(),
                ImmutableMap.of("bizCode", context.getBizCode(),
                        "abtestKey", Optional.ofNullable(context.getAbtestKey()).orElse("null")),
                () -> {
                    try {
                        handleContextData(context);
                        doPredict(context);
                        return context;
                    } finally {
                        handleResp(context);
                        LogTools.logContext(context);
                    }
                });
    }


    @NotNull
    private PredictContext doPredict(PredictContext context) {
        long t0 = System.currentTimeMillis();
        // abtest-distributor
        AbtestDistributor abtest = getAbtestByCode(context.getBizCode());

        // distribution
        String key = StringUtils.isNotBlank(context.getAbtestKey()) ? context.getAbtestKey() : context.getReq().toString();
        AbtestDistributor.DistributionConfig distribution = abtest.getDistributionConfig(key);
        context.setStrategy(abtest.getStrategyName(), distribution.getName());
        Long strategyId = distribution.getStrategyId();

        Map<String, ?> respMap;
        Map<String, String> respExtra = context.getRespExtra();

        PredictCacheConfig cacheConfig = getPredictCacheConfig(context.getBizCode(), context.getReqExtra());
        // 根据配置获取缓存key，可为空
        String cacheKey = getPredictCacheKey(context.getBizCode(), context.getReq(), strategyId, cacheConfig);
        PredictCacheContent sameParamCache = getSameParamCache(cacheKey, strategyId, cacheConfig);

        if (Objects.isNull(sameParamCache)) {
            // strategy
            AlgoStrategy strategy = getStrategyById(strategyId);
//            // predict
            respMap = strategy.run(context.getReq(), context.getContextData());
            //存缓存
            saveSameParamCache(cacheKey, respMap, strategyId, cacheConfig, context.getReq());
            respExtra.put("useCache", "false");
            if (StringUtils.isNotBlank(cacheKey)) {
                respExtra.put("cacheKey", cacheKey);
                respExtra.put("cacheVersion", strategy.getAlgoPackage().getVersion());
            }
            PREDICT_CACHE_NUM.report(context.getBizCode(), "false", context.getContextData());

        } else {
            respMap = sameParamCache.getCacheMap();
            PREDICT_CACHE_NUM.report(context.getBizCode(), "true", context.getContextData());
            respExtra.put("useCache", "true");
            if (StringUtils.isNotBlank(cacheKey)) {
                respExtra.put("cacheKey", cacheKey);
            }
            respExtra.put("cacheVersion", sameParamCache.getAlgorithmVersion());
        }
        //构造记录主键
        String uniqueKey = MD5Util.md5(JSON.toJSONString(context.getReq()));
        respExtra.put("unique_key", uniqueKey);

        context.setResp(respMap);
        context.setCost(t0);

        // 慢请求上报
        int slowRequestReportThreshold = Lion.getConfigRepository()
                .getIntValue(LionKeys.PREDICT_SLOW_REQUEST_REPORT_THRESHOLD, 10 * 60);
        if (System.currentTimeMillis() - t0 > slowRequestReportThreshold) {
            Predict_SlowRequest.report(context.getBizCode());
        }

        return context;
    }

    private PredictCacheConfig getPredictCacheConfig(String bizCode, Map<String, String> reqExtra) {
        // isTest代表为测试回放流量，不走缓存
        if (!MapUtils.isEmpty(reqExtra) && StringUtils.equals(reqExtra.get("isTest"), "1")) {
            return null;
        }
        //是否读缓存开关
        Boolean predictCacheSwitch = Lion.getConfigRepository().getBooleanValue(LionKeys.PREDICT_CACHE_SWITCH, false);
        if (!predictCacheSwitch) {
            return null;
        }

        // 为了能够方便全量清缓存，缓存key中增加了版本号，修改lion可以实现清全量缓存
        List<PredictCacheConfig> list = Lion.getConfigRepository().getList(LionKeys.PREDICT_CACHE_CONFIG, PredictCacheConfig.class);
        if (CollectionUtils.isEmpty(list)) {
            log.error("getPredictCacheKey 未配置缓存");
            return null;
        }
        // 根据bizCode判断是否走缓存
        Optional<PredictCacheConfig> cacheConfig = list.stream().filter(dto -> bizCode.equals(dto.getBizCode())).findFirst();
        if (!cacheConfig.isPresent()) {
            log.error("getPredictCacheKey 未配置缓存 bizCode={}", bizCode);
            return null;
        }

        PredictCacheConfig config = cacheConfig.get();

        return config;

    }

    private void saveSameParamCache(String cacheKey, Map<String, ?> respMap, Long strategyId, PredictCacheConfig cacheConfig, Map<String, ?> req) {

        //保存缓存失败，没必要影响主流程
        try {

            if (cacheConfig == null) {
                return;
            }
            if (StringUtils.isBlank(cacheKey)) {
                return;
            }
            //如果模型失败过，那么就不计入缓存
            String mtPoiId = getMtPoiIdFromReq(req);
            if (StringUtils.isNotBlank(mtPoiId)) {
                String errorLabel = tairClient.get(String.format(MODEL_ERROR_POI_CACHE, mtPoiId));
                if (StringUtils.isNotBlank(errorLabel)) {
                    return;
                }
            }

            PredictCacheContent content = new PredictCacheContent();
            content.setCacheMap(respMap);

            AlgoStrategyPo algoStrategyPo = algoStrategyDao.getValidByIdWithCache(strategyId);
            AlgoPackage algoPackage = algoPackageService.get(algoStrategyPo.getPackageId());
            //是否空只影响记录版本，不影响使用
            content.setAlgorithmVersion(Objects.isNull(algoPackage) ? "" : algoPackage.getVersion());

            tairClient.put(cacheKey, JSON.toJSONString(content), cacheConfig.getExpireMinutes() == null ? 2880 : cacheConfig.getExpireMinutes(), TimeUnit.MINUTES);

        } catch (Exception e) {
            log.error("saveSameParamCache error", e.getMessage(), e);
            Cat.logEvent("predictCache", "saveError");
        }


    }

    private String getMtPoiIdFromReq(Map<String, ?> req) {

        Object mtDeals = req.get("mt_deals");
        if (Objects.isNull(mtDeals)) {
            return null;
        }
        JSONArray mtDealJsonArray = JSON.parseArray(mtDeals.toString());
        if (mtDealJsonArray.isEmpty()) {
            return null;
        }
        JSONObject jsonObject = mtDealJsonArray.getJSONObject(0);
        String mtPoiId = jsonObject.getString("mt_poi_id");
        return mtPoiId;
    }

    private PredictCacheContent getSameParamCache(String cacheKey, Long strategyId, PredictCacheConfig cacheConfig) {

        try {
            if (StringUtils.isBlank(cacheKey)) {
                return null;
            }

            String cacheStr = tairClient.get(cacheKey);
            if (StringUtils.isBlank(cacheStr)) {
                return null;
            }
            PredictCacheContent content = JSON.parseObject(cacheStr, PredictCacheContent.class);

            //如果算法版本相同，那么延长时间,防止热数据缓存雪崩的问题
            AlgoStrategyPo algoStrategyPo = algoStrategyDao.getValidByIdWithCache(strategyId);
            AlgoPackage algoPackage = algoPackageService.get(algoStrategyPo.getPackageId());
            if (algoPackage != null && content != null) {
                String version = algoPackage.getVersion();
                if (Objects.equals(version, content.getAlgorithmVersion())) {
                    tairClient.put(cacheKey, cacheStr, cacheConfig.getExpireMinutes() == null ? 2880 : cacheConfig.getExpireMinutes(), TimeUnit.MINUTES);
                } else {
                    Cat.logEvent("predictAsync", "AlgoVersionChange");
                }
            }

            return content;
        } catch (Exception e) {
            log.error("getSameParamCache error", e.getMessage(), e);
            return null;
        }


    }


    private String getPredictCacheKey(String bizCode, Map<String, ?> req, Long strategyId, PredictCacheConfig cacheConfig) {


        if (cacheConfig == null || StringUtils.isBlank(cacheConfig.getVersion())) {
            return null;
        }
        String reqJson = JSON.toJSONString(req);
        //转md5
        String md5 = MD5Util.md5(reqJson);
        if (StringUtils.isBlank(md5)) {
            return null;
        }
        String cacheKey = String.format(PREDICT_CACHE_KEY_FORMAT, bizCode, cacheConfig.getVersion(), strategyId, md5);
        if (MdpContextUtils.isStagingEnv()) {
            cacheKey += "_staging";
        }
        log.info("getPredictCacheKey cacheKey={}", cacheKey);
        return cacheKey;
    }


    /**
     * 获取ab测试分流器
     *
     * @param bizCode bizCode
     * @return 分流器
     */
    private AbtestDistributor getAbtestByCode(String bizCode) {
        BizStrategyPo bizStrategyPo = bizStrategyDao.getValidByCodeWithCache(bizCode);
        Preconditions.checkArgument(bizStrategyPo != null, "bizCode异常:%s", bizCode);
        return AbtestDistributor.getInstance(bizStrategyPo.getAbtestConfig());
    }

    /**
     * 根据策略id获取算法策略
     *
     * @param id 策略id
     * @return AlgoStrategy
     */
    public AlgoStrategy getStrategyById(Long id) {
        AlgoStrategyPo strategyPo = algoStrategyDao.getValidByIdWithCache(id);
        Preconditions.checkNotNull(strategyPo, "策略异常:%s", id);
        AlgoPackage aPackage = algoPackageService.get(strategyPo.getPackageId());
        return new AlgoStrategy(strategyPo.getId(), aPackage, strategyPo.getEntrancePath(), strategyPo.getEntranceMethod());
    }

    private void handleResp(PredictContext context) {
        //精准匹配和模糊匹配离线结果压缩
        List<String> resultCompressedBizcodeList = Lion.getConfigRepository().getList(LionKeys.RESULT_COMPRESSED_BIZCODE,
                String.class, ImmutableList.of(""));
        if (resultCompressedBizcodeList.contains(context.getBizCode())
                && MapUtils.isNotEmpty(context.getResp())
                && MapUtils.isNotEmpty(context.getReqExtra())) {

            Map<String, Object> newMap = new HashMap<>();
            newMap.put("code", context.getResp().get("code"));
            newMap.put("match", context.getResp().get("match"));
            newMap.put("dj_full_match", context.getResp().get("dj_full_match"));

            if (context.getResp().get("match") != null && CollectionUtils.isNotEmpty((List) context.getResp().get("match"))) {
                String match = JSON.toJSONString(context.getResp().get("match"));
                String compressedResMatch = CompressUtil.compress(match, CompressUtil.COMPRESS_TYPE_GZIP);
                newMap.put("match", "");
                newMap.put("compressed_match", compressedResMatch);

            }
            if (context.getResp().get("dj_full_match") != null && CollectionUtils.isNotEmpty((List) context.getResp().get("dj_full_match"))) {
                String fullMatch = JSON.toJSONString(context.getResp().get("dj_full_match"));
                String compressedResFullMatch = CompressUtil.compress(fullMatch, CompressUtil.COMPRESS_TYPE_GZIP);
                newMap.put("dj_full_match", compressedResFullMatch);
            }
            context.setResp(newMap);
        }
    }

    private void handleContextData(PredictContext context) {
        try {
            Map<String, String> data = context.getContextData();
            data.put("bizCode", context.getBizCode());
            String traceId = ServerTracer.getInstance().getTraceId();
            Map<String, String> reqExtra = context.getReqExtra();
            if (MapUtils.isNotEmpty(reqExtra)) {
                String fullLinkTraceId = reqExtra.getOrDefault("traceId", "");
                data.put("fullLinkTraceId", fullLinkTraceId);
            }
            if (StringUtils.isNotEmpty(traceId)) {
                data.put("traceId", traceId);
                MDC.put("defineOutput", "#DLO#{__p2jTraceId__=" + traceId + "}#DLO#");
            }
            String secondCateName = getMtSecondCateName(context.getReq());
            if (StringUtils.isNotEmpty(secondCateName)) {
                data.put("cate", secondCateName);
            }
        } catch (Exception e) {
            log.error("PredictAppServiceImpl.handleContextData error. context:{}", JSON.toJSONString(context), e);
        }

    }

    private String getMtSecondCateName(Map<String, ?> req) {
        String mtDealsJson = (String) req.get("mt_deals");
        if (StringUtils.isNotBlank(mtDealsJson)) {
            JSONArray jsonArray = JSON.parseArray(mtDealsJson);
            if (!jsonArray.isEmpty()) {
                JSONObject mtDeal = jsonArray.getJSONObject(0);
                return mtDeal.getString("mt_second_cate_name");
            }
        }
        return "";
    }

}

