package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackage;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackageService;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoPackageDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.util.LocationUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AlgoPackageServiceImpl implements AlgoPackageService {
    private Map<Long, AlgoPackage> loadedPackages = new ConcurrentHashMap<>();
    @Resource
    private AlgoPackageDao algoPackageDao;
    @Resource
    private String pythonRuntime;

    @PostConstruct
    public void init() {
        Preconditions.checkState(StringUtils.equals("ok", pythonRuntime));
        refresh(Lion.getConfigRepository().getBooleanValue(LionKeys.THROW_ALGOPACK_INIT_ERROR, true));
    }

    @Override
    public int refresh(boolean throwError) {
        long t1 = System.currentTimeMillis();

        List<AlgoPackagePo> packagePos = algoPackageDao.getValidPackages();
        if (CollectionUtils.isEmpty(packagePos)) {
            return 0;
        }

        List<Long> added = new ArrayList<>();
        List<Long> changed = new ArrayList<>();
        for (AlgoPackagePo po : packagePos) {
            try {
                loadedPackages.compute(po.getId(), (k, oldVal) -> {
                    // 是否需要更新本地算法包缓存
                    if (oldVal == null ||
                            !Objects.equals(oldVal.getVersion(), po.getVersion()) ||
                            !Objects.equals(oldVal.getNote(), po.getNote()) ||
                            !Objects.equals(oldVal.getOwnerMis(), po.getOwnerMis()) ||
                            !Objects.equals(oldVal.getRuntime(), po.getRuntime()) ||
                            !Objects.equals(oldVal.getCodeRepo(), po.getCodeRepo()) ||
                            !Objects.equals(oldVal.getModulePath(), po.getModulePath())) {
                        AlgoPackage algoPackage = new AlgoPackage(po.getId(), po.getNote(), po.getOwnerMis(),
                                po.getRuntime(), po.getModulePath(), po.getVersion(), po.getCodeRepo());

                        RetryUtil.retry(algoPackage::pullCode, Objects::nonNull, 2, 500);
                        (oldVal == null ? added : changed).add(po.getId());
                        return algoPackage;
                    } else {
                        return oldVal;
                    }
                });
            } catch (RuntimeException e) {
                if (throwError) {
                    throw new RuntimeException("AlgoPackagePo update error: " + po.getId(), e);
                } else {
                    log.error("AlgoPackagePo update error: old:{}, new:{}, ex:", loadedPackages.get(po.getId()), po, e);
                }
            }
        }
        //删除tmp文件夹
        try {
            FileUtils.deleteDirectory(new File(LocationUtil.getRuntimePath() + "/python/tmp"));
        } catch (IOException e) {
            log.error("del tmp directory error", e);
        }

        Set<Long> poIds = packagePos.stream().map(AlgoPackagePo::getId).collect(Collectors.toSet());
        List<Long> toRemoveIds = loadedPackages.keySet().stream().filter(f -> !poIds.contains(f)).collect(Collectors.toList());
        toRemoveIds.forEach(loadedPackages::remove);
        log.info("AlgoPackageServiceImpl.loadedPackages {} loaded: ({} added, {} changed, {} removed). cost:{}",
                loadedPackages.keySet(), added, changed, toRemoveIds, (System.currentTimeMillis() - t1));
        return added.size() + changed.size();
    }

    @Override
    public AlgoPackage get(Long id) {
        return loadedPackages.get(id);
    }

    @Override
    public void loadPackage(Long id) {
        AlgoPackagePo po = algoPackageDao.getById(id);
        Preconditions.checkNotNull(po);
        AlgoPackage algoPackage = new AlgoPackage(po.getId(), po.getNote(), po.getOwnerMis(),
                po.getRuntime(), po.getModulePath(), po.getVersion(), po.getCodeRepo());
        boolean st = algoPackage.pullCode();
        Preconditions.checkState(st, "更新算法包失败:" + po.getId());
        loadedPackages.put(id, algoPackage);
    }

}
