package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

import lombok.Data;

import java.util.*;

@Data
public class PredictionQueryContext {

    public static PredictionQueryContext newInstance(String bizCode, List<String> entityIds, Map<String, String> extra) {
        PredictionQueryContext c = new PredictionQueryContext();
        c.setBizCode(bizCode);
        c.setEntityIds(Optional.ofNullable(entityIds).orElse(new ArrayList<>()));
        c.setReqExtra(Optional.ofNullable(extra).orElse(new HashMap<>()));
        return c;
    }

    // ----------- 请求相关 -----------
    private String bizCode;
    private List<String> entityIds;
    private Map<String, String> reqExtra;
    // ----------- 返回相关 -----------
    private Map<String, List<PredictionValue>> data = new LinkedHashMap<>();
    private Map<String, String> respExtra = new LinkedHashMap<>();

    @Data
    public static class PredictionValue {
        private Map<String, String> values = new LinkedHashMap<>();
    }
}
