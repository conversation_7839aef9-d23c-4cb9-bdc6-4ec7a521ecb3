package com.sankuai.meishi.stgy.algoplatform.predictor.service;

import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 模型预测
 */
public interface SimplePredictService {
    <T extends Number> Map<String, List<?>> predict(String modelName, Class<T> clazz, Map<String, List<List<T>>> feature);

    <T extends Number> Map<String, List<?>> predict(String modelName, Long modeVersion, String signatureName, Class<T> clazz, Map<String, List<List<T>>> feature);

    <T extends Number> Map<String, List<?>> predict(String modelName, Long modelVersion, String signatureName, Class<T> clazz, Map<String, List<List<T>>> feature, int dim);

    List<List<Double>> xgbPredict(String modelName, List<Map<String, Double>> featuresMapList);

    List<List<Double>> xgbPredict(String modelName, List<Map<String, Double>> featuresMapList, String defaultValue);

    List<List<Double>> xgbPredict(String modelName, Long modeVersion, String signatureName, List<Map<String, Double>> featuresMapList, String defaultValue);

    List<List<Double>> xgbPredictByMatrix(String modelName, List<List<Double>> features);

    LinkedHashMap<String, List<Object>> tritonPredict(String modelName, String modelVersion,
                                                      LinkedHashMap<String, List<Object>> inputValue, Map<String, List<Integer>> inputShape, Map<String, String> inputType,
                                                      LinkedHashMap<String, String> outputType);

    Map<String,List<?>> newTritonPredict(String modelName, String modelVersion, List<String> input);

    Map<String, Object> llmPredict(String bizCode, Map<String, String> prompts, Map<String, String> extra);

    List<Map<String, Object>> predictWithCache(PredictModelDto predictModelDto);



}
