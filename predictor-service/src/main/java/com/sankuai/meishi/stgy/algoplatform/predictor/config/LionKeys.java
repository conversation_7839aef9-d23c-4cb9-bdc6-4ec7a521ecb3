package com.sankuai.meishi.stgy.algoplatform.predictor.config;


public class LionKeys {
    /**
     * 算法包调用限流
     */
    public static final String BRIDGE_RATE_LIMIT = "bridgeRateLimit";
    /**
     * 算法Python环境异常自恢复开关
     */
    public static final String PYTHON_AUTO_RECOVER_SWITCH = "pythonAutoRecoverSwitch";
    /**
     * 算法Python环境进程实例数
     */
    public static final String PYTHON_PROCESS_NUM = "pythonProcessNum";
    /**
     * PredictContext日志上报开关
     */
    public static final String PREDICT_CONTEXT_LOG_SWITCH = "predictContextLogSwitch";
    /**
     * llmPredict日志上报开关
     */
    public static final String LLM_PREDICT_CONTEXT_LOG_SWITCH = "llmPredictContextLogSwitch";
    /**
     * 特征无缓存key日志上报开关
     */
    public static final String FEATURE_NO_CACHE_LOG_SWITCH = "featureNoCacheLogSwitch";
    /**
     * 测试环境特征mock
     */
    public static final String TEST_EVN_FEATURE_MOCK_DATA = "testEvnFeatureMockData";
    /**
     * 忽略特征查询失败情况
     */
    public static final String IGNORE_QUERY_FEATURE_EXCEPTION = "ignoreQueryFeatureException";
    /**
     * XGBSering服务LiteSet名称
     */
    public static final String XGB_MODEL_SERVING_LITESET_NAME = "xgbModelServingLiteSetName";
    /**
     * 判断是否set环境
     */
    public static final String PREDICT_LISTENER_SET_ENVIRONMENT_FLAG = "set_environment_flag:false";
    /**
     * 异步预测最大重试次数
     */
    public static final String PREDICT_ASYNC_MAX_RETRY_COUNT = "predict_async_max_retry_count";
    /**
     * 是否抛出算法包初始化异常
     */
    public static final String THROW_ALGOPACK_INIT_ERROR = "throw_algopack_init_error";
    /**
     * 算法预测是否使用缓存
     */
    public static final String PREDICT_CACHE_SWITCH = "predict_cache_switch";
    /**
     * 缓存配置
     */
    public static final String PREDICT_CACHE_CONFIG = "predict_cache_config";
    /**
     * 模型缓存配置
     */
    public static final String PREDICT_MODEL_CACHE_CONFIG = "predict_model_cache_config";
    /**
     * 服务mafka Set
     */
    public static final String SET_ENVIRONMENT_TYPE = "set_environment_type";
    /**
     * mafka 消费配置
     */
    public static final String MAFKA_CONSUMER_CONFIG = "mafka_consumer_config";
    /**
     * 模型调用最大重试次数
     */
    public static final String PREDICT_MODEL_RETRY_COUNT = "predict_model_retry_count";
    /**
     * 未命中缓存的菜品map拆分每批大小
     */
    public static final String PREDICT_NOCACHE_BATCH_COUNT = "predict_nocache_batch_count";
    /**
     * 预测慢请求上报阈值
     */
    public static final String PREDICT_SLOW_REQUEST_REPORT_THRESHOLD = "predict_slow_request_report_threshold";

    /**
     * 需要压缩结果的bizCode
     */
    public static final String RESULT_COMPRESSED_BIZCODE = "result_compressed_bizcode";

    /**
     * Mafka传入的数据被压缩的bizCode
     */
    public static final String MAFKA_COMPRESSED_BIZCODE = "mafka_compressed_bizcode";

    /**
     * 需要监控缓存开关的的bizCode
     */
    public static final String NEED_MONITOR_CACHE_BIZCODE_LIST = "need_monitor_cache_bizcode_list";

    /**
     * 需要监控的存活节点数阈值配置
     */
    public static final String OCTO_ALIVE_NODES_THRESHOLD = "octo_alive_nodes_threshold";

    /**
     * bizcode关联关系
     */
    public static final String PREDICT_BIZCODE_RELATION = "predict_bizcode_relation";

    /**
     * 到餐需要上报的bizCode
     */
    public static final String DC_NEED_REPORT_BICODE = "dc_need_report_bicode";

    /**
     * Python进程与启动时扩展字段配置
     */
    public static final String PYTHON_PROCESS_FEATURES = "pythonProcessFeatures";
}
