package com.sankuai.meishi.stgy.algoplatform.predictor.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;
import com.meituan.ai.friday.sdk.api.completion.chat.ChatCompletionChoice;
import com.meituan.hadoop.afo.jpmmlclient.JpmmlPredictClient;
import com.meituan.hadoop.afo.serving.client.thrift.MTThriftPredictClient;
import com.meituan.hadoop.afo.serving.common.OnlineService;
import com.meituan.hadoop.afo.triton.InferenceServerClient;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mtrace.Tracer;
import com.sankuai.algoplatform.llmpredict.predict.api.dto.LLMRequest;
import com.sankuai.algoplatform.llmpredict.predict.api.dto.LLMResponse;
import com.sankuai.algoplatform.llmpredict.predict.api.service.LLMService;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoStrategy;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.ModelCacheConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl.PredictAppServiceImpl;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.enumeration.ModelType;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.CatTransaction;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimpleFeatureService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimplePredictService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.util.TensorUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.util.TritonUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.DcDataReporter;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.*;
import inference.GrpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.tensorflow.framework.TensorProto;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants.*;
import static com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys.XGB_MODEL_SERVING_LITESET_NAME;
import static com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack.MODEL_4LEVEL_SECOND_CACHE_NUM;
import static com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack.MODEL_CACHE_NUM;

@Service
@Slf4j
public class SimplePredictServiceImpl implements SimplePredictService {
    @Resource
    private OnlineService.Iface afoClient;

    @Autowired
    private TairClient tairClient;

    @Autowired
    private SimpleFeatureService simpleFeatureService;

    @Autowired
    private PredictAppServiceImpl predictAppService;

    @MdpPigeonClient(remoteAppKey = LLM_PREDICT_SERVER_APPKEY, timeout = LLM_PREDICT_TIMEOUT)
    private LLMService llmClient;

    private final LoadingCache<String, MTThriftPredictClient> predictClientCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .build(new CacheLoader<String, MTThriftPredictClient>() {
                @Override
                public MTThriftPredictClient load(String key) throws Exception {
                    String[] sp = key.split("@");
                    MTThriftPredictClient client = new MTThriftPredictClient();
                    client.setClient(afoClient);
                    client.setModelName(sp[0]);
                    client.setModelVersion(Long.parseLong(sp[1]));
                    client.setSignatureName(sp[2]);
                    client.init();
                    log.info("initPredictClient: {} added.", key);
                    return client;
                }
            });
    private final LoadingCache<String, JpmmlPredictClient> xgbPredictClientCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .build(new CacheLoader<String, JpmmlPredictClient>() {
                @Override
                public JpmmlPredictClient load(String key) throws Exception {
                    String[] sp = key.split("@");
                    String modelName = sp[0];
                    long modelVersion = Long.parseLong(sp[1]);
                    String signatureName = sp[2];
                    JpmmlPredictClient client = new JpmmlPredictClient(
                            APPKEY, XGB_MODEL_SERVER_APPKEY,
                            modelName, modelVersion, signatureName,
                            XGB_MODEL_PREDICT_TIMEOUT
                    );
                    log.info("init xgbPredictClientCache: {} added.", key);
                    return client;
                }
            });
    private final LoadingCache<String, InferenceServerClient> tritonPredictClientCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .build(new CacheLoader<String, InferenceServerClient>() {
                @Override
                public InferenceServerClient load(String key) throws Exception {
                    InferenceServerClient client = new InferenceServerClient(
                            APPKEY, TRITON_MODEL_SERVER_APPKEY,
                            // 直连配置，上线前需要去掉
//                            "10.168.60.148:26358",
//                            "10.180.133.244:26369",
                            TRITON_MODEL_PREDICT_TIMEOUT, true
                    );
                    log.info("init InferenceServerClient: {} added.", key);
                    return client;
                }
            });

    private final LoadingCache<String, InferenceServerClient> newTritonPredictClientCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .build(new CacheLoader<String, InferenceServerClient>() {
                @Override
                public InferenceServerClient load(String key) throws Exception {
                    InferenceServerClient client = new InferenceServerClient(
                            APPKEY, DZ_TRITON_MODEL_SERVER_APPKEY,
                            // 直连配置，上线前需要去掉
//                            "10.168.60.148:26358",
//                            "10.180.133.244:26369",
                            TRITON_MODEL_PREDICT_TIMEOUT, true
                    );
                    log.info("init DzInferenceServerClient: {} added.", key);
                    return client;
                }
            });


    private MTThriftPredictClient getClient(String modelName, Long modelVersion, String signatureName) {
        String key = String.format("%s@%s@%s", modelName,
                Optional.ofNullable(modelVersion).orElse(-1L),
                Optional.ofNullable(signatureName).orElse("serving_default"));
        try {
            return predictClientCache.get(key);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    private JpmmlPredictClient getXGBClient(String modelName, Long modelVersion, String signatureName) {
        String key = String.format("%s@%s@%s", modelName,
                Optional.ofNullable(modelVersion).orElse(-1L),
                Optional.ofNullable(signatureName).orElse("serving_default"));
        try {
            return xgbPredictClientCache.get(key);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    private InferenceServerClient getTritonClient() {
        try {
            return tritonPredictClientCache.get("default");
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    private InferenceServerClient getNewTritonClient() {
        try {
            return newTritonPredictClientCache.get("default");
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "modelName")
    public <T extends Number> Map<String, List<?>> predict(String modelName, Class<T> clazz, Map<String, List<List<T>>> feature) {
        return predict(modelName, null, null, clazz, feature);
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "modelName")
    public <T extends Number> Map<String, List<?>> predict(String modelName, Long modeVersion, String signatureName, Class<T> clazz, Map<String, List<List<T>>> feature) {
        if (MapUtils.isEmpty(feature)) {
            return Collections.emptyMap();
        }
        List<Integer> dims = feature.values().stream().flatMap(Collection::stream)
                .map(Collection::size)
                .distinct()
                .collect(Collectors.toList());
        Preconditions.checkArgument(dims.size() == 1, "无法确定向量长度, %s", dims);
        return predict(modelName, modeVersion, signatureName, clazz, feature, dims.get(0));
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "modelName")
    public <T extends Number> Map<String, List<?>> predict(String modelName, Long modeVersion, String signatureName, Class<T> clazz, Map<String, List<List<T>>> feature, int dim) {
        if (MapUtils.isEmpty(feature)) {
            return Collections.emptyMap();
        }
        long t0 = System.currentTimeMillis();
        Map<String, TensorProto> tfReq = new HashMap<>(Maps.transformValues(
                feature,
                v -> {
                    if (Integer.class.equals(clazz)) {
                        List<Integer> res = v.stream().flatMap(Collection::stream)
                                .map(Number::intValue).collect(Collectors.toList());
                        return TensorUtil.buildIntTensor(res, v.size(), dim);
                    }
                    if (Long.class.equals(clazz)) {
                        List<Long> res = v.stream().flatMap(Collection::stream)
                                .map(Number::longValue).collect(Collectors.toList());
                        return TensorUtil.buildLongTensor(res, v.size(), dim);
                    }
                    if (Float.class.equals(clazz)) {
                        List<Float> res = v.stream().flatMap(Collection::stream)
                                .map(Number::floatValue).collect(Collectors.toList());
                        return TensorUtil.buildFloatTensor(res, v.size(), dim);
                    }
                    if (Double.class.equals(clazz)) {
                        List<Double> res = v.stream().flatMap(Collection::stream)
                                .map(Number::doubleValue).collect(Collectors.toList());
                        return TensorUtil.buildDoubleTensor(res, v.size(), dim);
                    }
                    throw new IllegalStateException("unsupported type:" + clazz);
                })
        );

        MTThriftPredictClient client = getClient(modelName, modeVersion, signatureName);
        Map<String, TensorProto> resp;
        try {
            resp = client.predict(tfReq);
        } catch (Exception e) {
            throw new RuntimeException(String.format("predict error, modelName:%s", modelName), e);
        }
        Map<String, List<?>> r = new HashMap<>(Maps.transformValues(
                resp,
                SimplePredictServiceImpl::trans2List
        ));
        if (log.isDebugEnabled()) {
            log.debug("SimplePredictService.predict: modelName:{}, modeVersion:{}, signatureName:{}, clazz:{}, dim:{}, size:{}, cost:{}, feature:{}, r:{}",
                    modelName, modeVersion, signatureName, clazz, dim, r.size(), System.currentTimeMillis() - t0, feature, r);
        } else {
            log.info("SimplePredictService.predict: modelName:{}, modeVersion:{}, signatureName:{}, clazz:{}, dim:{}, size:{}, cost:{}",
                    modelName, modeVersion, signatureName, clazz, dim, r.size(), System.currentTimeMillis() - t0);
        }
        return r;
    }

    private static List<?> trans2List(TensorProto proto) {
        if (proto.getTensorShape().getDimCount() == 1) {
            return TensorUtil.getNumber(proto);
        } else if (proto.getTensorShape().getDimCount() == 2) {
            List<Number> numbers = (List<Number>) TensorUtil.getNumber(proto);
            List<List<Number>> res = new ArrayList<>();
            int dim0 = (int) proto.getTensorShape().getDim(0).getSize();
            int dim1 = (int) proto.getTensorShape().getDim(1).getSize();
            for (int i = 0; i < dim0; i++) {
                res.add(new ArrayList<>(dim1));
                for (int j = 0; j < dim1; j++) {
                    res.get(i).add(0);
                }
            }
            for (int i = 0; i < numbers.size(); i++) {
                int a0 = i / dim1;
                int a1 = i % dim1;
                res.get(a0).set(a1, numbers.get(i));
            }
            return res;
        } else {
            throw new IllegalStateException("unexpected dim value: " + proto.getTensorShape().getDimCount());
        }
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "modelName")
    public List<List<Double>> xgbPredict(String modelName, List<Map<String, Double>> featuresMapList) {
        return xgbPredict(modelName, null, null, featuresMapList, null);
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "modelName")
    public List<List<Double>> xgbPredict(String modelName, List<Map<String, Double>> featuresMapList, String defaultValue) {
        return xgbPredict(modelName, null, null, featuresMapList, defaultValue);
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "modelName")
    public List<List<Double>> xgbPredict(String modelName, Long modeVersion, String signatureName, List<Map<String, Double>> featuresMapList, String defaultValue) {
        if (CollectionUtils.isEmpty(featuresMapList)) {
            return Collections.emptyList();
        }
        long t0 = System.currentTimeMillis();
        // gray-release-data-xxxx, 后缀为任务名称,在mlp模型服务页面上可以看到
        String sourceCell = Tracer.getCell();
        String liteSetName = Objects.requireNonNull(Lion.getConfigRepository().get(XGB_MODEL_SERVING_LITESET_NAME));
        JpmmlPredictClient xgbClient = getXGBClient(modelName, modeVersion, signatureName);
        double[][] predictResult = null;
        try {
            Tracer.setCell(liteSetName);
            if (defaultValue != null) {
                predictResult = xgbClient.batchPredictByXGB(featuresMapList, defaultValue);
            } else {
                predictResult = xgbClient.batchPredictByXGB(featuresMapList);
            }
        } catch (Exception exception) {
            log.error("xgbPredict error: modelName:{}, e:", modelName, exception);
            throw new RuntimeException(String.format("xgbPredict error: modelName: %s", modelName), exception);
        } finally {
            if (sourceCell == null) {
                Tracer.clearContext(Tracer.CELL);
            } else {
                Tracer.setCell(sourceCell);
            }
            if (log.isDebugEnabled()) {
                log.debug("SimplePredictService.xgbPredict features={},defaultValue={}, result:{}",
                        JSON.toJSONString(featuresMapList), defaultValue, predictResult);
            }
        }
        log.info("SimplePredictService.xgbPredict: modelName:{}, modeVersion:{}, signatureName:{}, dim:{}, size:{}, cost:{}",
                modelName, modeVersion, signatureName, featuresMapList.size(), featuresMapList.get(0).size(), System.currentTimeMillis() - t0);
        return Arrays.stream(predictResult)
                .map(row -> Arrays.stream(row).boxed().collect(Collectors.toList()))
                .collect(Collectors.toList());
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "modelName")
    public List<List<Double>> xgbPredictByMatrix(String modelName, List<List<Double>> features) {
        if (CollectionUtils.isEmpty(features)) {
            return Collections.emptyList();
        }
        long t0 = System.currentTimeMillis();
        String sourceCell = Tracer.getCell();
        String liteSetName = Objects.requireNonNull(Lion.getConfigRepository().get(XGB_MODEL_SERVING_LITESET_NAME));
        JpmmlPredictClient xgbClient = getXGBClient(modelName, null, null);
        double[][] predictResult;
        try {
            Tracer.setCell(liteSetName);
            predictResult = xgbClient.batchPredictByXGB2(features);
            // 结果转化
            List<List<Double>> res = Lists.newArrayListWithCapacity(predictResult.length);
            for (double[] doubles : predictResult) {
                List<Double> list = Lists.newArrayListWithCapacity(predictResult[0].length);
                res.add(list);
                for (int j = 0; j < predictResult[0].length; j++) {
                    list.add(doubles[j]);
                }
            }
            return res;
        } catch (Exception e) {
            throw new RuntimeException(String.format("xgbPredictByMatrix error: modelName: %s", modelName), e);
        } finally {
            if (sourceCell == null) {
                Tracer.clearContext(Tracer.CELL);
            } else {
                Tracer.setCell(sourceCell);
            }
            log.info("SimplePredictService.xgbPredictByMatrix: modelName:{}, dim:({},{}), cost:{}",
                    modelName, features.size(), features.size() > 0 ? features.get(0).size() : 0, System.currentTimeMillis() - t0);
        }
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "modelName")
    public LinkedHashMap<String, List<Object>> tritonPredict(String modelName, String modelVersion,
                                                             LinkedHashMap<String, List<Object>> inputValue, Map<String, List<Integer>> inputShape, Map<String, String> inputType,
                                                             LinkedHashMap<String, String> outputType) {
        long t0 = System.currentTimeMillis();
        // request
        GrpcService.ModelInferRequest.Builder request = GrpcService.ModelInferRequest.newBuilder();
        request.setModelName(modelName);
        request.setModelVersion(modelVersion);
        TritonUtil.buildTensorByShape(request, inputValue, inputShape, inputType, new ArrayList<>(outputType.keySet()));
        // predict
        GrpcService.ModelInferResponse response;
        try {
            response = getTritonClient().predict(request.build());
        } catch (Exception e) {
            throw new RuntimeException(String.format("predict error, modelName:%s", modelName), e);
        }
        // output
        List<ByteString> outputContentsList = response.getRawOutputContentsList();
        LinkedHashMap<String, List<Object>> outputContent = TritonUtil.buildOutputContent(outputType, outputContentsList);
        log.info("SimplePredictService.tritonPredict: modelName:{}, modeVersion:{}, inputShape:{}, inputType:{}, outputType:{}, cost:{}",
                modelName, modelVersion, inputShape, inputType, outputType, System.currentTimeMillis() - t0);
        return outputContent;
    }

    private LinkedHashMap<String, List<Object>> tritonPredict(String modelName, String modelVersion,
                                                             LinkedHashMap<String, List<Object>> inputValue, Map<String, List<Integer>> inputShape, Map<String, String> inputType,
                                                             LinkedHashMap<String, String> outputType, InferenceServerClient client) {
        long t0 = System.currentTimeMillis();
        // request
        GrpcService.ModelInferRequest.Builder request = GrpcService.ModelInferRequest.newBuilder();
        request.setModelName(modelName);
        request.setModelVersion(modelVersion);
        TritonUtil.buildTensorByShape(request, inputValue, inputShape, inputType, new ArrayList<>(outputType.keySet()));
        // predict
        GrpcService.ModelInferResponse response;
        try {
            response = client.predict(request.build());
        } catch (Exception e) {
            throw new RuntimeException(String.format("predict error, modelName:%s", modelName), e);
        }
        // output
        List<ByteString> outputContentsList = response.getRawOutputContentsList();
        LinkedHashMap<String, List<Object>> outputContent = TritonUtil.buildOutputContent(outputType, outputContentsList);
        log.info("SimplePredictService.tritonPredict: modelName:{}, modeVersion:{}, inputShape:{}, inputType:{}, outputType:{}, cost:{}",
                modelName, modelVersion, inputShape, inputType, outputType, System.currentTimeMillis() - t0);
        return outputContent;
    }

    private LinkedHashMap<String, List<JSONObject>> parseOutput(LinkedHashMap<String, List<Object>> outputContent) {
        LinkedHashMap<String, List<JSONObject>> result = new LinkedHashMap<>();
        for (Map.Entry<String, List<Object>> entrySet : outputContent.entrySet()) {
            String key = entrySet.getKey();
            List<Object> value = entrySet.getValue();
            List<JSONObject> valueJSONList = new ArrayList<>(value.size());
            for (Object o : value) {
                String valueStr = o.toString();
//                int i = valueStr.indexOf("{\"");
//                String substring = valueStr.substring(i);
                JSONObject valueJson = JSONObject.parseObject(valueStr);
                valueJSONList.add(valueJson);
            }
            result.put(key, valueJSONList);
        }
        return result;
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "modelName")
    public Map<String,List<?>> newTritonPredict(String modelName, String modelVersion, List<String> input) {
        LinkedHashMap<String, List<Object>> inputValue = new LinkedHashMap<>();
        List<Object> inputData = new ArrayList<>(input);
        inputValue.put("INPUT", inputData);

        Map<String, List<Integer>> inputShape = new HashMap<>();
        inputShape.put("INPUT", Lists.newArrayList(1,inputData.size()));
        Map<String, String> inputType = new HashMap<>();
        inputType.put("INPUT", "string");
        LinkedHashMap<String,String> outputType = new LinkedHashMap<>();
        outputType.put("OUTPUT", "string");

        LinkedHashMap<String, List<Object>> predictResult = tritonPredict(modelName, modelVersion, inputValue, inputShape, inputType, outputType, getNewTritonClient());
        LinkedHashMap<String, List<JSONObject>> formatOutputContent = parseOutput(predictResult);
        JSONObject output = formatOutputContent.get("OUTPUT").get(0);
        Map<String, List<String>> formatResult = output.toJavaObject(new TypeReference<LinkedHashMap<String, List<String>>>() {});
        Map<String, List<String>> sortedResult = sortResultMap(formatResult, input);
        Map<String, List<?>> result = mergeOutput(sortedResult);
        return result;
    }

    private Map<String, List<String>> sortResultMap(Map<String, List<String>> formatResult, List<String> keyOrder) {
        // 将Map条目放入List中
        List<Map.Entry<String, List<String>>> entryList = new ArrayList<>(formatResult.entrySet());

        // 根据指定的顺序对List进行排序
        entryList.sort(Comparator.comparingInt(entry -> keyOrder.indexOf(entry.getKey())));

        // 将排序后的条目重新放入一个LinkedHashMap中以保持顺序
        Map<String, List<String>> sortResult = entryList.stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
        return sortResult;
    }

    private Map<String, List<?>> mergeOutput(Map<String, List<String>> formatResult) {
        Map<String, List<?>> result = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : formatResult.entrySet()) {
            List<List<String>> formatData = new ArrayList<>();
            List<String> value = entry.getValue();
            String joinedData = "";
            if (value != null) {
                joinedData = String.join("#", entry.getValue());
            }
            formatData.add(Lists.newArrayList(joinedData));
            result.put(entry.getKey(), formatData);
        }
        return result;
    }

    @Override
    @CatTransaction(type = "SimplePredictService", fieldValAsName = "bizCode")
    public Map<String, Object> llmPredict(String bizCode, Map<String, String> prompts, Map<String, String> extra) {
        long t0 = System.currentTimeMillis();

        Map<String, Object> logRequest = Maps.newHashMap();
        Map<String, String> logRequestExtra = Maps.newHashMap();
        Map<String, Object> logResponse = Maps.newHashMap();
        Map<String, String> logResponseExtra = Maps.newHashMap();

        Map<String, Object> result = Maps.newHashMap();
        Pair<Boolean, Object> promptsCheckResult = checkAndGetLLMPrompts(bizCode, prompts);

        if (promptsCheckResult.getLeft()) {
            List<Map<String, String>> promptsList = (List<Map<String, String>>) promptsCheckResult.getRight();
            LLMRequest llmRequest = createLLMRequest(bizCode, promptsList, extra);
            logRequestExtra.putAll(ImmutableMap.of("llmRequest", JSONObject.toJSONString(llmRequest)));
            LLMResponse llmResponse;
            try {
                llmResponse = llmClient.inferenceForRpc(llmRequest);
            } catch (Exception e) {
                throw new RuntimeException(String.format("llmPredict error, bizCode:%s", bizCode), e);
            }
            logResponseExtra.put("llmResponse", JSONObject.toJSONString(llmResponse));
            result.putAll(processLLMResponse(llmResponse, extra));
        } else {
            logResponseExtra.put("promptsCheckFailMessage", (String) promptsCheckResult.getRight());
            result.put("code", 1);
        }
        logRequest.putAll(ImmutableMap.of("bizCode", bizCode, "prompts", prompts, "extra", extra));
        logResponse.putAll(result);

        PredictContext llmPredictContext = PredictContext.newInstance(bizCode, "", logRequest, logRequestExtra);
        llmPredictContext.setResp(logResponse);
        llmPredictContext.setRespExtra(logResponseExtra);
        LogTools.logLlmContext(llmPredictContext);

        log.info("SimplePredictService.llmPredict: bizCode:{}, prompts:{}, extra:{}, resultCode:{}, cost:{}",
                bizCode, prompts, extra, result.get("code"), System.currentTimeMillis() - t0);
        return result;
    }

    private Pair<Boolean, Object> checkAndGetLLMPrompts(String bizCode, Map<String, String> prompts) {
        if (StringUtils.isEmpty(bizCode) || MapUtils.isEmpty(prompts) || !prompts.containsKey("prompts")) {
            return Pair.of(false, "参数不合法");
        }
        List<Map<String, String>> promptsList = JSONObject.parseObject(prompts.get("prompts"), new TypeReference<List<Map<String, String>>>() {});
        if (CollectionUtils.isEmpty(promptsList)) {
            return Pair.of(false, "prompt为空");
        }

        long validPromptsCount = promptsList.stream()
                .filter(map -> map.containsKey("key") && StringUtils.isNotEmpty(map.get("key")))
                .map(map -> map.get("key"))
                .distinct()
                .count();
        if (validPromptsCount != promptsList.size()) {
            return Pair.of(false, "key校验不通过");
        }

        return Pair.of(true, promptsList);
    }

    private LLMRequest createLLMRequest(String bizCode, List<Map<String, String>> promptsList, Map<String, String> extra) {
        String role = "user";
        if (MapUtils.isNotEmpty(extra) && StringUtils.isNotEmpty(extra.get("role"))) {
            role = extra.get("role");
        }

        Map<String, String> req = Maps.newHashMap();
        req.put("role", role);
        req.put("prompt", JSONObject.toJSONString(promptsList));

        LLMRequest llmRequest = new LLMRequest();
        llmRequest.setReq(req);
        llmRequest.setBizCode(bizCode);
        return llmRequest;
    }

    private Map<String, Object> processLLMResponse(LLMResponse llmResponse, Map<String, String> extra) {
        Map<String, Object> output = Maps.newHashMap();
        if (llmResponse.getCode() == 0 && MapUtils.isNotEmpty(llmResponse.getData())
                && Objects.nonNull(llmResponse.getData().get("result"))) {
            String result = llmResponse.getData().get("result");
            Map<String, ChatCompletionChoice> chatCompletionChoiceMap = JSONObject.parseObject(result, new TypeReference<Map<String, ChatCompletionChoice>>() {});

            Map<String, String> data = Maps.newHashMap();
            chatCompletionChoiceMap.entrySet().stream()
                    .filter(entry ->
                            "stop".equals(entry.getValue().getFinishReason())
                                    && Objects.nonNull(entry.getValue().getMessage()))
                    .forEach(entry -> {
                        String key = entry.getKey();
                        data.put(key, entry.getValue().getMessage().getContent());
                    });
            output.put("data", data);

            if (MapUtils.isNotEmpty(extra) && Boolean.TRUE.toString().equalsIgnoreCase(extra.get("debug_result"))) {
                output.put("extra", ImmutableMap.of("debug_result", result));
            }

            output.put("code", llmResponse.getCode());
        } else {
            output.put("code", -1);
        }
        return output;
    }

    @Override
    public List<Map<String, Object>> predictWithCache(PredictModelDto predictModelDto) {

        try {
            List<Map<String, Object>> maps = doPredictWithCache(predictModelDto);
            DcDataReporter.reportModelInfo(predictModelDto, maps, true);
            return maps;
        } catch (Exception e) {
            log.error("predictWithCache error: {}, ex:", JSON.toJSONString(predictModelDto), e);
            LogTools.logModelLog(predictModelDto);
            // 记录模型报错的poi
            tairClient.put(String.format(MODEL_ERROR_POI_CACHE, predictModelDto.getMt_poi_id()), "1", 60, TimeUnit.MINUTES);
            Cat.logEvent("SimplePredictServiceImpl", "predictWithCacheError");
            DcDataReporter.reportModelInfo(predictModelDto, new ArrayList<>(), false);
            return null;
        }

    }

    public List<Map<String, Object>> doPredictWithCache(PredictModelDto predictModelDto) {

        /**
         * {
         *   "input":[["手工面", "手擀面"],["白菜", "小白菜"]],
         *   "func_type":"bi_convert/single_batch_convert",
         *   "max_seq_len":512,
         *     "model_name":"","groupId":""
         * }
         */
        ModelCacheConfig cacheConfig = Lion.getConfigRepository().getBean(LionKeys.PREDICT_MODEL_CACHE_CONFIG, ModelCacheConfig.class);

        if (cacheConfig == null) {
            log.error("predictWithCache 缓存配置错误");
            return null;
        }

        if (CollectionUtils.isEmpty(predictModelDto.getInput()) && CollectionUtils.isEmpty(predictModelDto.getProcessed_input())) {
            RaptorTrack.PREDICT_INPUT_EMPTY.report(predictModelDto.getModel_name());
        }

        List<List<String>> inputList = predictModelDto.getInput();
        boolean isProcessed;
        if (CollectionUtils.isNotEmpty(predictModelDto.getProcessed_input())) {
            inputList = predictModelDto.getProcessed_input();
            isProcessed = true;
        } else {
            isProcessed = false;
        }

        if (inputList == null) {
            inputList = new ArrayList<>();
        }

        String globalCacheKey = String.format(PREDICT_MODEL_GLOBAL_CACHE_FORMAT, predictModelDto.getModel_name(), predictModelDto.getFunc_type()
                , predictModelDto.getMax_seq_len(), cacheConfig.getCacheVersion(), MD5Util.md5(JSON.toJSONString(inputList)));
        if (MdpContextUtils.isStagingEnv()) {
            globalCacheKey += "_staging";
        }

        //1.查询全局缓存
        List<Map<String, Object>> globalCache = queryGlobalCache(globalCacheKey, cacheConfig);

        if (CollectionUtils.isNotEmpty(globalCache)) {
            // 打点
            RaptorTrack.MODEL_GLOBAL_CACHE_NUM.report(predictModelDto.getModel_name(), "true",
                    predictModelDto.getContextData());
            return globalCache;
        } else {
            RaptorTrack.MODEL_GLOBAL_CACHE_NUM.report(predictModelDto.getModel_name(), "false",
                    predictModelDto.getContextData());
        }

        //2.循环里面的菜品对/deal对或者菜品，每对查询缓存，如果有缓存，保留，没有的继续往下处理

        Map<String, List<String>> nameMap = inputList.stream().collect(Collectors.toMap(innerList ->
                        isProcessed ? innerList.get(0) : String.format(COUPLE_SEPARATE, innerList.get(0), innerList.get(1)),
                Function.identity(),
                (key1, key2) -> key1
        ));

        //能查到菜品对缓存的菜品对集合,不需要菜品数据继续计算了（a菜品｜b菜品 :0.98）
        Map<String, String> coupleCache = getCoupleCache(predictModelDto, cacheConfig);

        // 获取模型预估缓存
        List<String> featureList = new ArrayList<>(nameMap.keySet());
        featureList.removeIf(coupleCache::containsKey);

        // Pair left=feature2cacheKey  right=cacheKey2feature
        Pair<Map<String, String>, Map<String, String>> pair = buildCacheKey(featureList, predictModelDto, cacheConfig);

        Map<String, Object> modelPredictCache = getModelPredictCache(predictModelDto, cacheConfig, pair.getRight());

        //不能查到菜品对集合的，需要查模型的数据
        Map<String, List<String>> noCacheMap = getNoCacheNameMap(nameMap, coupleCache, modelPredictCache);

        final List<List<List<String>>> modelPredictResultList = new ArrayList<>();
        final List<List<List<Map<String, List<String>>>>> modelPredictResultListMatrix = new ArrayList<>();

        if (MapUtils.isNotEmpty(noCacheMap)) {
            LogTools.logNoCache(predictModelDto, noCacheMap.keySet());
            // noCacheMap 数据量过大拆分
            int batchCount = Lion.getConfigRepository().getIntValue(LionKeys.PREDICT_NOCACHE_BATCH_COUNT, 500);
            List<Map<String, List<String>>> noCacheMapList = MapUtil.partition(noCacheMap, batchCount);

            int maxRetryCount = Lion.getConfigRepository().getIntValue(LionKeys.PREDICT_MODEL_RETRY_COUNT, 3);

            for (Map<String, List<String>> ncMap : noCacheMapList) {
                //3.以下是没有缓存的数据，批量调用预处理接口
                ModelType modelType = predictModelDto.getModelTypeEnum();
                AtomicReference<Map<String, List<List<Integer>>>> preHandleResult = new AtomicReference<>(new HashMap<>());
                Map<String, ?> preHandleResultRaw = new HashMap<>();
                if (ModelType.TF.equals(modelType)) {
                    preHandleResultRaw = invokePreHandle(predictModelDto, ncMap, cacheConfig);  // strategyId需要按照业务配置;
                }
                if (predictModelDto.isNotMatrix()) {
                    preHandleResult.set((Map<String, List<List<Integer>>>) preHandleResultRaw);
                } else {
                    preHandleResult.set((Map<String, List<List<Integer>>>) preHandleResultRaw.get("model_input"));
                }

                Map<String, ?> finalPreHandleResultRaw = preHandleResultRaw;
                RetryUtil.retry(() -> {
                            //4.再调用TF/Torch模型服务
                            Map<String, List<?>> predictResult = new HashMap<>();
                            if (ModelType.TF.equals(modelType)) {
                                predictResult = RaptorTrack.take("SimplePredictService", predictModelDto.getModel_name(), () ->
                                        predict(predictModelDto.getModel_name(), Integer.class, preHandleResult.get()));
                            } else if (ModelType.TORCH.equals(modelType)){
                                predictResult = RaptorTrack.take("SimplePredictService", predictModelDto.getModel_name(), () ->
                                        newTritonPredict(predictModelDto.getModel_name(), "", new ArrayList<>(ncMap.keySet())));
                            } else {
                                RaptorTrack.Sys_UnexpectedVisitNum.report("Unknown modelType, modelName is " + predictModelDto.getModel_name());
                            }
                            if (MapUtils.isNotEmpty(predictResult)) {
                                if (predictModelDto.isNotMatrix()) {
                                    List<List<List<String>>> modelResult = JSON.parseObject(JSON.toJSONString(predictResult.values()), new TypeReference<List<List<List<String>>>>() {
                                    });
                                    modelPredictResultList.addAll(modelResult);
                                } else {
                                    Map<String, List<List<String>>> extra = (Map<String, List<List<String>>>) finalPreHandleResultRaw.get("extra");
                                    List<List<List<Map<String, List<String>>>>> modelResult = modelResultOfMatrix(predictResult, extra);
                                    modelPredictResultListMatrix.addAll(modelResult);
                                }

                            }
                        }, ex -> ex != null,
                        maxRetryCount, 500L);

            }
        }

        //5.汇总缓存和模型返回的数据
        List<Map<String, Object>> result = Lists.newArrayList();
        result.addAll(summaryCacheResult(coupleCache, modelPredictCache, predictModelDto));
        List<Map<String, Object>> noCacheResult;
        if (predictModelDto.isNotMatrix()) {
            noCacheResult = summaryNoCacheResult(modelPredictResultList, noCacheMap, predictModelDto);
        } else {
            noCacheResult = summaryNoCacheResultMatrix(modelPredictResultListMatrix, noCacheMap, predictModelDto);
        }
        result.addAll(noCacheResult);
        // 保存四级缓存
        saveModelPredictCache(noCacheResult, cacheConfig, pair.getLeft());
        //排序
        result = sortResult(result, predictModelDto);
        // 保存二级缓存
        savePredictCache(globalCacheKey, result, cacheConfig);
        return result;
    }

    private List<Map<String, Object>> sortResult(List<Map<String, Object>> result, PredictModelDto predictModelDto) {
        List<Map<String, Object>> sortedResult = new ArrayList<>();
        if (!predictModelDto.isNotMatrix()) {
            List<List<String>> input = predictModelDto.getProcessed_input();
            for (List<String> keyList : input) {
                String key = keyList.get(0);
                result.stream()
                        .filter(map -> map.containsKey(key))
                        .findFirst()
                        .ifPresent(sortedResult::add);
            }
        } else {
            sortedResult = result;
        }
        return sortedResult;
    }


    private static List<List<List<Map<String, List<String>>>>> modelResultOfMatrix(Map<String, List<?>> predictResult,
                                                                                   Map<String, List<List<String>>> extra) {
        ArrayList<String> keys = new ArrayList<>(predictResult.keySet());
        int size = predictResult.get(keys.get(0)).size();
        ArrayList<String> extraKeys = new ArrayList<>(extra.keySet());

        List<List<List<Map<String, List<String>>>>> outputList = new ArrayList<>();

        List<List<Map<String, List<String>>>> outerList = new ArrayList<>();

        for (int i = 0; i < size; i++) {
            Map<String, List<String>> currentMap = new HashMap<>();
            for (String key : keys) {
                currentMap.put(key, (List<String>) predictResult.get(key).get(i));
            }
            for (String key : extraKeys) {
                currentMap.put(key, extra.get(key).get(i));
            }
            List<Map<String, List<String>>> innerList = new ArrayList<>();
            innerList.add(currentMap);
            outerList.add(innerList);
        }
        outputList.add(outerList);
        return outputList;
    }

    private Pair<Map<String, String>, Map<String, String>> buildCacheKey(List<String> featureList,
                                                                         PredictModelDto predictModelDto,
                                                                         ModelCacheConfig cacheConfig) {
        if (BooleanUtils.isNotTrue(cacheConfig.getModelPredictCacheSwitch())) {
            return Pair.of(Collections.emptyMap(), Collections.emptyMap());
        }
        Map<String, String> feature2cacheKey = new HashMap<>();
        Map<String, String> cacheKey2feature = new HashMap<>();

        String env = MdpContextUtils.getHostEnvStr();
        for (String feature : featureList) {
            String cacheKey = String.format(PREDICT_MODEL_RESULT_CACHE_FORMAT,
                    predictModelDto.getModel_name(), predictModelDto.getFunc_type(),
                    predictModelDto.getMax_seq_len(), MD5Util.md5(feature), env);
            feature2cacheKey.put(feature, cacheKey);
            cacheKey2feature.put(cacheKey, feature);
        }
        return Pair.of(feature2cacheKey, cacheKey2feature);
    }

    private List<Map<String, Object>> summaryCacheResult(Map<String, String> coupleCache, Map<String, Object> modelPredictCache,
                                                         PredictModelDto predictModelDto) {
        List<Map<String, Object>> resultList = Lists.newArrayList();
        Map<String, String> newCoupleCache = Safes.of(coupleCache);
        newCoupleCache.forEach((k, v) -> {
            Map<String, Object> map = new HashMap<>();
            if (predictModelDto.getCache_type() == 0) {
                map.put(k, Float.parseFloat(v));
            } else {
                map.put(k, v);
            }
            if (!predictModelDto.isNotMatrix()) {
                map.put("out_type", 0);
            }
            resultList.add(map);
        });

        Map<String, Object> newModelPredictCache = Safes.of(modelPredictCache);
        newModelPredictCache.forEach((k, v) -> {
            Map<String, Object> map = new HashMap<>();
            map.put(k, v);
            if (!predictModelDto.isNotMatrix()) {
                map.put("out_type", 1);
            }
            resultList.add(map);
        });

        MODEL_CACHE_NUM.report(predictModelDto.getModel_name(), "true", newCoupleCache.size(),
                predictModelDto.getContextData());
        MODEL_CACHE_NUM.report(predictModelDto.getModel_name(), "false", newModelPredictCache.size(),
                predictModelDto.getContextData());
        MODEL_4LEVEL_SECOND_CACHE_NUM.report(predictModelDto.getModel_name(), "true", newModelPredictCache.size(),
                predictModelDto.getContextData());
        MODEL_4LEVEL_SECOND_CACHE_NUM.report(predictModelDto.getModel_name(), "false", newCoupleCache.size(),
                predictModelDto.getContextData());
        return resultList;
    }

    private List<Map<String, Object>> summaryNoCacheResult(List<List<List<String>>> predictModelList, Map<String, List<String>> noCacheMap,
                                                           PredictModelDto predictModelDto) {
        List<Map<String, Object>> resultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(predictModelList)) {
            List<List<String>> modelResultList = predictModelList.stream().flatMap(List::stream).collect(Collectors.toList());
            Set<String> keySet = noCacheMap.keySet();
            Iterator<List<String>> valueiterator = modelResultList.iterator();
            Iterator<String> keyIterator = keySet.iterator();

            Integer modelIndex = predictModelDto.getModelIndex() == null ? 1 : predictModelDto.getModelIndex();
            ModelType modelType = predictModelDto.getModelTypeEnum();
            while (valueiterator.hasNext() && keyIterator.hasNext()) {
                Map<String, Object> map = new HashMap<>();
                if (ModelType.TF.equals(modelType)) {
                    map.put(keyIterator.next(), Float.parseFloat(valueiterator.next().get(modelIndex)));
                } else if (ModelType.TORCH.equals(modelType)) {
                    map.put(keyIterator.next(), valueiterator.next().get(0));
                }
                if (!predictModelDto.isNotMatrix()) {
                    map.put("out_type", 0);
                }
                resultList.add(map);
            }
            MODEL_CACHE_NUM.report(predictModelDto.getModel_name(), "false", modelResultList.size(),
                    predictModelDto.getContextData());
            MODEL_4LEVEL_SECOND_CACHE_NUM.report(predictModelDto.getModel_name(), "false", modelResultList.size(),
                    predictModelDto.getContextData());
        }
        return resultList;
    }

    /**
     * 处理模型返回结构为矩阵的结果
     */
    private List<Map<String, Object>> summaryNoCacheResultMatrix(List<List<List<Map<String, List<String>>>>> predictModelList,
                                                                 Map<String, List<String>> noCacheMap,
                                                                 PredictModelDto predictModelDto) {
        List<Map<String, Object>> resultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(predictModelList)) {
            List<List<Map<String, List<String>>>> modelResultList = predictModelList.stream().flatMap(List::stream).collect(Collectors.toList());
            List<String> keys = new ArrayList<>(noCacheMap.keySet());
            for (int i = 0; i < keys.size() && i < modelResultList.size(); i++) {
                Map<String, Object> map = new HashMap<>();
                map.put(keys.get(i), modelResultList.get(i).get(0));
                map.put("out_type", 1);
                resultList.add(map);
            }
            MODEL_CACHE_NUM.report(predictModelDto.getModel_name(), "false", modelResultList.size(),
                    predictModelDto.getContextData());
            MODEL_4LEVEL_SECOND_CACHE_NUM.report(predictModelDto.getModel_name(), "false", modelResultList.size(),
                    predictModelDto.getContextData());
        }
        return resultList;
    }

    private void saveModelPredictCache(List<Map<String, Object>> noCacheResult,
                                       ModelCacheConfig cacheConfig, Map<String, String> feature2cacheKey) {
        if (BooleanUtils.isNotTrue(cacheConfig.getModelPredictCacheSwitch())) {
            return;
        }
        Integer modelCacheBatchPutNum = cacheConfig.getModelCacheBatchPutNum();
        AtomicInteger ai = new AtomicInteger();
        Map<String, String> kv = new HashMap<>();
        for (Map<String, Object> map : noCacheResult) {
            Map<String, String> finalKv = kv;
            map.forEach((k, v) -> {
                String key = feature2cacheKey.get(k);
                if (key != null) {
                    finalKv.put(key, JsonUtils.toJson(v));
                }
            });
            if (ai.addAndGet(map.size()) >= modelCacheBatchPutNum) {
                tairClient.batchPut(kv, cacheConfig.getExpireMinutes(), TimeUnit.MINUTES);
                kv = new HashMap<>();
                ai.set(0);
            }
        }
        if (!kv.isEmpty()) {
            tairClient.batchPut(kv, cacheConfig.getExpireMinutes(), TimeUnit.MINUTES);
        }
    }

    private Map<String, List<String>> getNoCacheNameMap(Map<String, List<String>> nameMap, Map<String, String> coupleCache,
                                                        Map<String, Object> modelPredictCache) {

        if (MapUtils.isEmpty(coupleCache) && MapUtils.isEmpty(modelPredictCache)) {
            return new HashMap<>(nameMap);
        }

        Map<String, List<String>> noCacheMap = new HashMap<>();

        for (Map.Entry<String, List<String>> nameEntry : nameMap.entrySet()) {

            if (!coupleCache.containsKey(nameEntry.getKey()) && !modelPredictCache.containsKey(nameEntry.getKey())) {
                //没命中缓存
                noCacheMap.put(nameEntry.getKey(), nameEntry.getValue());
            }
        }
        return noCacheMap;
    }

    private <T> void savePredictCache(String globalCacheKey, T result, ModelCacheConfig cacheConfig) {
        if (!cacheConfig.getGlobalCacheSwitch()) {
            return;
        }
        tairClient.put(globalCacheKey, JSON.toJSONString(result), cacheConfig.getExpireMinutes(), TimeUnit.MINUTES);
    }

    //    private Map<String, List<List<Integer>>> invokePreHandle(PredictModelDto predictModelDto, Map<String, List<String>> noCacheMap, ModelCacheConfig cacheConfig) {
    private Map<String, ?> invokePreHandle(PredictModelDto predictModelDto, Map<String, List<String>> noCacheMap, ModelCacheConfig cacheConfig) {

        Map invokeMap = new HashMap();
        invokeMap.put("max_seq_len", predictModelDto.getMax_seq_len());
        invokeMap.put("func_type", predictModelDto.getFunc_type());
        invokeMap.put("examples", noCacheMap.values());

        // strategy
        AlgoStrategy strategy = predictAppService.getStrategyById(cacheConfig.getStrategyId());
        // predict
        return strategy.run(invokeMap, new HashMap<>());

    }

    private Map<String, String> getCoupleCache(PredictModelDto predictModelDto, ModelCacheConfig cacheConfig) {
        if (StringUtils.isEmpty(predictModelDto.getGroupId()) || StringUtils.equals(predictModelDto.getGroupId(), "-1")) {
            return new HashMap<>();
        }

        if (!cacheConfig.getCoupleCacheSwitch()) {
            return Collections.emptyMap();
        }

        List<List<String>> input = predictModelDto.getInput();
        ModelType modelType = predictModelDto.getModelTypeEnum();
        boolean isProcessed;
        if (CollectionUtils.isNotEmpty(predictModelDto.getProcessed_input())) {
            input = predictModelDto.getProcessed_input();
            isProcessed = true;
        } else {
            isProcessed = false;
        }

        if (input == null) {
            return new HashMap<>();
        }

        Map<String, String> nameMap = new HashMap<>();

        List<Map<String, String>> featureKeys = new ArrayList<>();


        input.stream().forEach(
                couple -> {
                    String content = couple.get(0);
                    if (!isProcessed) {
                        content = String.format(COUPLE_SEPARATE, couple.get(0), couple.get(1));
                    }
                    String cacheKey = "";
                    if (ModelType.TF.equals(modelType)) {
                        cacheKey = String.format(PREDICT_MODEL_COUPLE_CACHE_FORMAT, predictModelDto.getModel_name(), predictModelDto.getFunc_type(), predictModelDto.getMax_seq_len(), MD5Util.md5(content));
                    } else if (ModelType.TORCH.equals(modelType)) {
                        cacheKey = String.format(TORCH_PREDICT_MODEL_COUPLE_CACHE_FORMAT, predictModelDto.getModel_name(), predictModelDto.getMax_seq_len(), MD5Util.md5(content));
                    } else {
                        RaptorTrack.Sys_UnexpectedVisitNum.report("Unknown modelType, modelName is " + predictModelDto.getModel_name());
                    }
                    Map featureMap = new HashMap();
                    featureMap.put("cache_key", cacheKey);
                    featureKeys.add(featureMap);
                    nameMap.put(cacheKey, content);
                }
        );

        try {
            // 最大传100
            Map<String, String> totalResultMap = new HashMap<>();

            int batchSize = cacheConfig.getModelCacheBatchNum(); // 每个子列表的大小

            List<List<Map<String, String>>> subLists = new ArrayList<>();

            for (int i = 0; i < featureKeys.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, featureKeys.size());
                List<Map<String, String>> subList = featureKeys.subList(i, endIndex);
                subLists.add(subList);
            }

            for (List<Map<String, String>> subList : subLists) {

                List<Map<String, String>> cache = simpleFeatureService.query(predictModelDto.getGroupId(), subList);
                Map<String, String> subMap = convertCacheToMap(cache, subList, nameMap);
                if (MapUtils.isNotEmpty(subMap)) {
                    totalResultMap.putAll(subMap);
                }
            }
            return totalResultMap;
        } catch (Exception e) {
            log.error("getCoupleCache error, keys:{}, e:", JSON.toJSONString(featureKeys), e);
            return Collections.emptyMap();
        }

    }

    private Map<String, Object> getModelPredictCache(PredictModelDto predictModelDto,
                                                     ModelCacheConfig cacheConfig, Map<String, String> cacheKey2feature) {
        if (BooleanUtils.isNotTrue(cacheConfig.getModelPredictCacheSwitch())) {
            return Collections.emptyMap();
        }
        Map<String, Object> modelPredictCache = Maps.newHashMap();

        int batchSize = cacheConfig.getModelCacheBatchNum();
        List<Map<String, String>> partition = MapUtil.partition(cacheKey2feature, batchSize);
        for (Map<String, String> batchCacheKeyMap : partition) {
            Map<String, String> map = tairClient.batchGet("", batchCacheKeyMap.keySet());

            ModelType modelType = predictModelDto.getModelTypeEnum();
            if (MapUtils.isNotEmpty(map)) {
                map.forEach((k, v) -> {
                    if (predictModelDto.isNotMatrix()) {
                        if (ModelType.TF.equals(modelType)) {
                            modelPredictCache.put(batchCacheKeyMap.get(k), Float.parseFloat(v));
                        } else if (ModelType.TORCH.equals(modelType)) {
                            modelPredictCache.put(batchCacheKeyMap.get(k), JSON.parse(v));
                        }
                    } else {
                        Map<String, List<Object>> res = JSON.parseObject(v, new TypeReference<Map<String, List<Object>>>() {
                        });
                        modelPredictCache.put(batchCacheKeyMap.get(k), res);
                    }
                });
                if (cacheConfig.isModelPredictCacheRefresh()) {
                    tairClient.batchPut(map, cacheConfig.getExpireMinutes(), TimeUnit.MINUTES);
                }
            }
        }
        return modelPredictCache;
    }

    private Map<String, String> convertCacheToMap(List<Map<String, String>> cache, List<Map<String, String>> featureKeys, Map<String, String> nameMap) {

        /**
         * cache [{"dish_name_model_sim":"0.9862"},{},{"dish_name_model_sim":"0.0018"}]
         * featureKeys [{"cache_key":"dish_name_sim_model_single_batch_convert_64_e102a80718763dcbe96385920356c955"},{"cache_key":"dish_name_sim_model_single_batch_convert_64_59911f4788418416582ec4b6f590eb0c"},{"cache_key":"dish_name_sim_model_single_batch_convert_64_9724cbf5e6f6ebbf26cad976f417930c"}]
         * nameMap:{"dish_name_sim_model_single_batch_convert_64_9724cbf5e6f6ebbf26cad976f417930c":"白菜|小白菜","dish_name_sim_model_single_batch_convert_64_e102a80718763dcbe96385920356c955":"手工面|手擀面","dish_name_sim_model_single_batch_convert_64_59911f4788418416582ec4b6f590eb0c":"白菜|ss白菜"}
         */
        if (CollectionUtils.isEmpty(cache) || CollectionUtils.isEmpty(featureKeys)) {
            return null;
        }
        Map<String, String> resultMap = new HashMap<>();
        Iterator<Map<String, String>> cacheIterator = cache.iterator();
        Iterator<Map<String, String>> featureKeyIterator = featureKeys.iterator();

        while (cacheIterator.hasNext() && featureKeyIterator.hasNext()) {

            Map<String, String> cacheMap = cacheIterator.next();
            Map<String, String> featureMap = featureKeyIterator.next();

            if (MapUtils.isNotEmpty(cacheMap)) {
                //有缓存
                String cacheValue = cacheMap.values().stream().findFirst().orElse(null);
                String cacheKey = featureMap.values().stream().findFirst().orElse(null);
                String name = nameMap.get(cacheKey);
                if (StringUtils.isNotBlank(cacheValue) && StringUtils.isNotBlank(name))
                    resultMap.put(name, cacheValue);
            }
        }
        return resultMap;
    }

    private List<Map<String, Object>> queryGlobalCache(String globalCacheKey, ModelCacheConfig cacheConfig) {

        if (!cacheConfig.getGlobalCacheSwitch()) {
            return null;
        }
        String globalCache = tairClient.get(globalCacheKey);

        if (StringUtils.isBlank(globalCache)) {
            return null;
        } else {
            if (cacheConfig.isGlobalCacheRefresh()) {
                tairClient.put(globalCacheKey, globalCache, cacheConfig.getExpireMinutes(), TimeUnit.MINUTES);
            }
            return JSON.parseObject(globalCache, new TypeReference<List<Map<String, Object>>>() {
            });
        }
    }
}

