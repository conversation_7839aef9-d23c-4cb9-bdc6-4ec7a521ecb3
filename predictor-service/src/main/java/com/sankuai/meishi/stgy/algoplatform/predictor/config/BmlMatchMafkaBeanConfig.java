package com.sankuai.meishi.stgy.algoplatform.predictor.config;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.BmlMatchSceneExtraConfigDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BmlMatchSceneExtraConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
@DependsOn({"predictListener"})
public class BmlMatchMafkaBeanConfig implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        try {
            init();
        } catch (Exception e) {
            log.error("init mafka fail", e);
            throw new RuntimeException(e);
        }
    }

    @Resource
    private BmlMatchSceneExtraConfigDao dao;

    /**
     * 创建匹配结果Mafka生产者Bean
     */
    public void init() throws Exception {
        List<BmlMatchSceneExtraConfig> allConfig = dao.selectAllActiveConfigs();

        Set<String> algoSet = new HashSet<>();

        allConfig.forEach(l -> {
            String deployInfoConfig = l.getDeployInfoConfig();
            if (StringUtils.isEmpty(deployInfoConfig)) {
                return;
            }
            Map<String, String> config = JSONObject.parseObject(deployInfoConfig, new TypeReference<Map<String, String>>() {
            });
            if (StringUtils.isNotEmpty(config.get("consumerAlgoSetName"))) {
                algoSet.add(config.get("consumerAlgoSetName"));
            }
        });


        //初始化生产者bean
        List<String> producerAlgoList = new ArrayList<>(algoSet);
        initProducer(producerAlgoList);
    }

    /**
     * 形式：producer-algo-fast-food
     */
    public void initProducer(List<String> cells) {
        for (String setName : cells) {
            setName = handleCellName(setName);
            //主干生产者已经在BeanConfig中初始化
            if (StringUtils.isEmpty(setName)) {
                continue;
            }
            IProducerProcessor processor = createAlgoProducerProcessor(setName);
            String beanName = getMafkaProducerBeanName(setName);
            if (processor == null) {
                RaptorTrack.Sys_UnexpectedVisitNum.report("BmlMatchMafkaInitFail");
                log.error("BmlMatchMafkaBeanConfig.initProducer bean is null.beanName={}", beanName);
                return;
            }
            SpringContextUtils.registerSingletonBean(beanName, processor);
        }
    }

    public static String getMafkaProducerBeanName(String setName) {
        return "producer-" + setName;
    }

    /**
     * 匹配结果生产者
     */
    public IProducerProcessor createAlgoProducerProcessor(String setName) {
        try {
            Properties properties = new Properties();
            properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daocan");
            properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.algoplatform.predictor");
            // 指定TopicSet发送消息
            if (StringUtils.isNotEmpty(setName)) {
                properties.setProperty(ConsumerConstants.CELL_NAME, setName);
            }
            IProducerProcessor<Object, Object> process = MafkaClient.buildProduceFactory(properties,
                    "algoplatform.predictor.predict.result");
            return process;
        } catch (Exception e) {
            log.error("BmlMatchMafkaBeanConfig.initProducer 创建匹配结果生产者失败.setName={}", setName, e);
            return null;
        }
    }

    public static String handleCellName(String setName) {
        if (StringUtils.equals(setName, "default")
                || StringUtils.equals(setName, "default-cell")
                || Objects.isNull(setName)) {
            setName = "";
        }
        return setName;
    }
}
