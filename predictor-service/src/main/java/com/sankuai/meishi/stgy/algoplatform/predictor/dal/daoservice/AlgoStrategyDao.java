package com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice;

import com.google.common.base.Preconditions;
import com.meituan.mdp.boot.starter.mdpcache.anno.Cached;
import com.meituan.mdp.boot.starter.mdpcache.core.CacheType;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoStrategyExample;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.AlgoStrategyMapper;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AlgoStrategyDao {
    @Resource
    private AlgoStrategyMapper algoStrategyMapper;

    @CatTransaction(type = "DaoService", name = "AlgoStrategyDao.getValidByIdWithCache")
    @Cached(area = "a1", key = "#id", cacheType = CacheType.LOCAL, timeUnit = TimeUnit.SECONDS, expire = 10, localLimit = 1000)
    public AlgoStrategyPo getValidByIdWithCache(Long id) {
        AlgoStrategyExample strategyExample = new AlgoStrategyExample();
        strategyExample.createCriteria().andStatusEqualTo(0)
                .andIdEqualTo(id);
        List<AlgoStrategyPo> algoStrategyPos = algoStrategyMapper.selectByExample(strategyExample);
        if (CollectionUtils.isEmpty(algoStrategyPos)) {
            return null;
        }
        Preconditions.checkArgument(algoStrategyPos.size() == 1, "getValidByIdWithCache 不唯一: %s", id);
        return algoStrategyPos.get(0);
    }
}
