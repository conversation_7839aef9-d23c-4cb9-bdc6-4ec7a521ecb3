package com.sankuai.meishi.stgy.algoplatform.predictor.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackageService;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class AlgoPackageTask {
    @Resource
    private AlgoPackageService algoPackageService;

    @Crane(Constants.APPKEY + "_refreshPackages")
    public void refreshPackages() {

        try {
            TimeUnit.SECONDS.sleep(new Random().nextInt(30) + 1);
        } catch (InterruptedException e) {
            log.error("", e);
        }

        int refreshCnt = algoPackageService.refresh(false);
        log.info("refreshPackages: {} packages added/changed.", refreshCnt);
    }
}
