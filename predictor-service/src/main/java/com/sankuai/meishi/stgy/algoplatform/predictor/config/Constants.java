package com.sankuai.meishi.stgy.algoplatform.predictor.config;

public class Constants {
    public static final String APPKEY = "com.sankuai.algoplatform.predictor";
    public static final String TF_MODEL_SERVER_APPKEY = "com.sankuai.algoplatform.modelserver";
    public static final String XGB_MODEL_SERVER_APPKEY = "com.sankuai.algoplatform.modelserver.xgb";
    public static final String TRITON_MODEL_SERVER_APPKEY = "com.sankuai.algoplatform.modelserver.pytorch";
    public static final String DZ_TRITON_MODEL_SERVER_APPKEY = "com.sankuai.algoplatform.dzmoderserver.pytorch";
    public static final String LLM_PREDICT_SERVER_APPKEY = "com.sankuai.algoplatform.llmpredict";
    /**
     * 算法包Git仓库地址
     */
    public static final String ALGO_PACKAGE_GIT_URL = "ssh://*******************/dcgstgy/algoplat-package.git";
    /**
     * TF模型预测超时时间
     */
    public static final int TF_MODEL_PREDICT_TIMEOUT = 60000;
    /**
     * XGB模型预测超时时间
     */
    public static final int XGB_MODEL_PREDICT_TIMEOUT = 5;
    /**
     * TRITON模型预测超时时间
     */
    public static final int TRITON_MODEL_PREDICT_TIMEOUT = 60 * 1000 * 10;
    /**
     * llmpredict超时时间
     */
    public static final int LLM_PREDICT_TIMEOUT = 10000;
    /**
     * 缓存key格式 算法类型+算法版本+(stID)+md5(req)
     */
    public static final String PREDICT_CACHE_KEY_FORMAT="%s_%s_%s_%s";
    /**
     * 模型全局缓存 modelName+fun+len+version+md5(input)
     */
    public static final String PREDICT_MODEL_GLOBAL_CACHE_FORMAT="%s_%s_%s_%s_%s";
    /**
     * 模型菜品对/deal对缓存 modelName+fun+len+md5(input)
     */
    public static final String PREDICT_MODEL_COUPLE_CACHE_FORMAT="%s_%s_%s_%s";
    /**
     * 模型菜品对/deal对缓存 modelName+len+md5(input)
     */
    public static final String TORCH_PREDICT_MODEL_COUPLE_CACHE_FORMAT="%s_%s_%s";
    /**
     * 模型菜品对/deal对结果缓存 modelName+fun+len+md5(input)+env
     */
    public static final String PREDICT_MODEL_RESULT_CACHE_FORMAT="%s_%s_%s_%s_%s";
    /**
     * 请求对分隔符
     */
    public static final String COUPLE_SEPARATE="%s|%s";
    /**
     * 模型错误的poi记录
     */
    public static final String MODEL_ERROR_POI_CACHE="model_error_mt_poiId_%s";



}
