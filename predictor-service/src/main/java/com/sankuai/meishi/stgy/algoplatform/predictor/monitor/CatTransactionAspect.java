package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Component
@Aspect
@Order(0)
@Slf4j
public class CatTransactionAspect {

    @Around("@annotation(CatTransaction)")
    public Object log(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Object target = joinPoint.getTarget();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        String catType, catName = target.getClass().getSimpleName() +
                "." +
                signature.getName();
        CatTransaction annotation = method.getAnnotation(CatTransaction.class);
        if (annotation != null && StringUtils.isNotBlank(annotation.type())) {
            catType = annotation.type();
        } else {
            catType = CatTransaction.DEFAULT_TYPE;
        }
        if (annotation != null) {
            if (StringUtils.isNotBlank(annotation.name())) {
                catName = annotation.name();
            } else if (StringUtils.isNotBlank(annotation.fieldValAsName())) {
                String fieldName = annotation.fieldValAsName();
                try {
                    Object res = ParamUtil.getFieldValueByName(fieldName, method, args);
                    catName = String.valueOf(res);
                    if (catName.length() > 50) {
                        catName = catName.substring(0, 50) + "...";
                    }
                } catch (Exception e) {
                    log.error("getFieldValueByName occurred error:", e);
                }
            }
        }

        Object result;
        Transaction t = Cat.newTransaction(catType, catName);
        try {
            result = joinPoint.proceed(args);
            t.setStatus(Event.SUCCESS);
        } catch (Exception e) {
            t.setStatus(e);
            throw e;
        } finally {
            t.complete();
        }
        return result;
    }
}
