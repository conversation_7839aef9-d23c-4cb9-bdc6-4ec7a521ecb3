package com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BmlMatchSceneExtraConfig {
    private Long id;

    private Long matchSceneId;

    private String dataMappingConfig;

    private String resultProcessConfig;

    private String deployInfoConfig;

    private String description;

    private String owner;

    private Byte status;

    private Date addTime;

    private Date updateTime;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BmlMatchSceneExtraConfig other = (BmlMatchSceneExtraConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getMatchSceneId() == null ? other.getMatchSceneId() == null : this.getMatchSceneId().equals(other.getMatchSceneId()))
            && (this.getDataMappingConfig() == null ? other.getDataMappingConfig() == null : this.getDataMappingConfig().equals(other.getDataMappingConfig()))
            && (this.getResultProcessConfig() == null ? other.getResultProcessConfig() == null : this.getResultProcessConfig().equals(other.getResultProcessConfig()))
            && (this.getDeployInfoConfig() == null ? other.getDeployInfoConfig() == null : this.getDeployInfoConfig().equals(other.getDeployInfoConfig()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getOwner() == null ? other.getOwner() == null : this.getOwner().equals(other.getOwner()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getMatchSceneId() == null) ? 0 : getMatchSceneId().hashCode());
        result = prime * result + ((getDataMappingConfig() == null) ? 0 : getDataMappingConfig().hashCode());
        result = prime * result + ((getResultProcessConfig() == null) ? 0 : getResultProcessConfig().hashCode());
        result = prime * result + ((getDeployInfoConfig() == null) ? 0 : getDeployInfoConfig().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getOwner() == null) ? 0 : getOwner().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }
}