package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.alibaba.fastjson.JSON;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.nib.data.zb.datatrace.platform.sdk.dto.TraceDataDTO;
import com.sankuai.nib.data.zb.datatrace.platform.sdk.enums.DomainEnum;
import com.sankuai.nib.data.zb.datatrace.platform.sdk.util.TraceDataReportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class BmlDataReportUtil {

    public static void reportData(PReportMessage pReportMessage) {
        if (null == pReportMessage) {
            return;
        }

        try {
            Map<String, Object> customData = new HashMap<>();
            if (MapUtils.isNotEmpty(pReportMessage.getCustomData())) {
                customData = pReportMessage.getCustomData();
            }
            customData.put("req", pReportMessage.getReq());


            TraceDataDTO traceData = TraceDataDTO
                    .builder()
                    .traceId(pReportMessage.getTraceId())
                    .domain(DomainEnum.MATCH.getDesc())
                    .eventDesc(pReportMessage.getEventDesc())
                    .mtPoiId(pReportMessage.getMtPoiId())
                    .mtItemId(pReportMessage.getMtDealId())
                    .poiId(pReportMessage.getDjPoiId())//竞对
                    .itemId(pReportMessage.getDjDealId())//竞对
                    .message(pReportMessage.getResp())
                    .customData(JSON.toJSONString(customData))
                    .businessDate(System.currentTimeMillis())   //上报时间
                    .businessStatus(pReportMessage.isSuccess()) //
                    .businessId(pReportMessage.getBusinessId())
                    .extendDataList(pReportMessage.getExtendDataList())
                    .bu(pReportMessage.getBuEnum().getDesc())
                    .build();

            if (log.isDebugEnabled()) {
                log.debug("BmlDataReportUtil.reportData:{}", JSON.toJSONString(traceData));
            }
            RaptorTrack.take("BmlReportData", pReportMessage.getEventDesc(), () -> {
                TraceDataReportUtil.reportData(traceData);
                return null;
            });
        } catch (Exception e) {
            log.error("上报数据异常：", e);
        }
    }

}
