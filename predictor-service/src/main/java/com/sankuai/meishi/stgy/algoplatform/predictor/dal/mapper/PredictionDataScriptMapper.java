package com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.PredictionDataScriptExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PredictionDataScriptMapper extends MybatisBaseMapper<PredictionDataScriptPo, PredictionDataScriptExample, Long> {
    int batchInsert(@Param("list") List<PredictionDataScriptPo> list);
}