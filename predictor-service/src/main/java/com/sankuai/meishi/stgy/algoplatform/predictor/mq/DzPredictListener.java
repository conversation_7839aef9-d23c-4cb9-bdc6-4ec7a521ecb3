package com.sankuai.meishi.stgy.algoplatform.predictor.mq;

import com.amazonaws.util.IOUtils;
import com.dianping.cat.Cat;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.s3.AmazonS3ClientUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.s3.AmazonS3Service;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.TPredictServicePublish;
import com.sankuai.wenchang.infer.service.entity.AlgoProducerPack;
import com.sankuai.wenchang.infer.utils.ByteUtil;
import com.sankuai.wenchang.infer.utils.CompressUtil;
import com.sankuai.wenchang.infer.utils.GsonCasual;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2024年03月28日 2:03 下午
 */
@Component
@Slf4j
public class DzPredictListener implements IMessageListener<String> {
    @Autowired
    private AmazonS3Service amazonS3Service;

    @Autowired
    private TPredictServicePublish tPredictServicePublish;

    private final static String bucketName = "dzdml-match";

    private AlgoProducerPack parseMessage(String msgBody) throws Exception {
        if (msgBody.startsWith("fileName=")){
            String fileName = msgBody.split("fileName=")[1];
            File file = amazonS3Service.getFile(AmazonS3ClientUtil.getBmlS3Client(), bucketName, fileName);
            InputStream inputStream = new FileInputStream(file);
            String content = IOUtils.toString(inputStream);
            AlgoProducerPack algoProducerPack = GsonCasual.fromJson(content, AlgoProducerPack.class);
            return algoProducerPack;
        } else {
            String unCompressMsg = new String(CompressUtil.gzipUncompress(ByteUtil.hex2Bytes(msgBody)));
            AlgoProducerPack algoProducerPack = GsonCasual.fromJson(unCompressMsg, AlgoProducerPack.class);
            return algoProducerPack;
        }
    }

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        String msgBody = null;
        try {
            msgBody = (String) mafkaMessage.getBody();
            AlgoProducerPack algoProducerPack = parseMessage(msgBody);
            TPredictRequest tPredictRequest = new TPredictRequest();
            tPredictRequest.setBizCode(algoProducerPack.getBizCode());
            tPredictRequest.setReq(algoProducerPack.getReq());
            tPredictRequest.setAbtestKey(algoProducerPack.getAbtestKey());
            tPredictRequest.setExtra(algoProducerPack.getExtra());
            tPredictServicePublish.dzPredictWithPreprocess(tPredictRequest);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("PredictListener#recvMessage error: msg:{}, ex:", msgBody, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }
}
