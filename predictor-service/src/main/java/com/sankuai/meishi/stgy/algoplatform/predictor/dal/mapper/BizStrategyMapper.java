package com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BizStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.BizStrategyExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BizStrategyMapper extends MybatisBaseMapper<BizStrategyPo, BizStrategyExample, Long> {
    int batchInsert(@Param("list") List<BizStrategyPo> list);
}