package com.sankuai.meishi.stgy.algoplatform.predictor.python.util;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.transport.JschConfigSessionFactory;
import org.eclipse.jgit.transport.OpenSshConfig;
import org.eclipse.jgit.transport.SshSessionFactory;
import org.eclipse.jgit.transport.SshTransport;
import org.eclipse.jgit.util.FS;

import java.io.File;
import java.io.IOException;

public class GitUtil {
    private static SshSessionFactory sshSessionFactory;

    static {
        init();
    }

    public static void init() {
        if (sshSessionFactory == null) {
            String idRsa, idRsaPub;
            try {
                idRsa = Kms.getByName(Constants.APPKEY, "git_ssh_id_rsa");
                idRsaPub = Kms.getByName(Constants.APPKEY, "git_ssh_id_rsa_pub");
            } catch (KmsResultNullException e) {
                throw new IllegalStateException(e);
            }

            sshSessionFactory = new JschConfigSessionFactory() {
                @Override
                protected void configure(OpenSshConfig.Host host, Session session) {
                    session.setConfig("StrictHostKeyChecking", "no");
                }

                @Override
                protected JSch createDefaultJSch(FS fs) throws JSchException {
                    JSch defaultJSch = new JSch();
                    defaultJSch.addIdentity("git_algoplatpackager", idRsa.getBytes(), idRsaPub.getBytes(), null);
                    return defaultJSch;
                }
            };
        }
    }

    public static void clone(String remoteRepoPath, String localRepoPath) throws GitAPIException {
        Git git = null;
        try {
            git = Git.cloneRepository()
                    .setURI(remoteRepoPath)
                    .setTransportConfigCallback(transport -> {
                        SshTransport sshTransport = (SshTransport) transport;
                        sshTransport.setSshSessionFactory(sshSessionFactory);
                    })
                    .setDirectory(new File(localRepoPath)) //设置下载存放路径
                    .call();
        } finally {
            if (git != null) {
                git.close();
            }
        }
    }

    public static void checkout(String localRepoPath, String commitId) throws IOException, GitAPIException {
        Git.open(new File(localRepoPath))
                .checkout()
                .setCreateBranch(true)
                .setName("checkout-" + commitId)
                .setStartPoint(commitId)
                .call();
    }

    private GitUtil() {

    }
}
