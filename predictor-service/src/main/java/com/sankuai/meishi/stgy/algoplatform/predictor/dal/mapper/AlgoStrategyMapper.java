package com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoStrategyExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AlgoStrategyMapper extends MybatisBaseMapper<AlgoStrategyPo, AlgoStrategyExample, Long> {
    int batchInsert(@Param("list") List<AlgoStrategyPo> list);
}