package com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity;

import lombok.*;

import java.util.Date;

/**
 * 表名: prediction_data_script
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PredictionDataScriptPo {
    /**
     * 字段: id
     * 说明: 自增主键
     */
    private Long id;

    /**
     * 字段: biz_code
     * 说明: 业务标识
     */
    private String bizCode;

    /**
     * 字段: note
     * 说明: 业务说明
     */
    private String note;

    /**
     * 字段: owner_mis
     * 说明: 负责人
     */
    private String ownerMis;

    /**
     * 字段: script
     * 说明: 脚本
     */
    private String script;

    /**
     * 字段: script_type
     * 说明: 脚本类型
     */
    private String scriptType;

    /**
     * 字段: status
     * 说明: 状态，0：正常，-1：删除
     */
    private Integer status;

    /**
     * 字段: add_time
     * 说明: 添加时间
     */
    private Date addTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    private Date updateTime;
}