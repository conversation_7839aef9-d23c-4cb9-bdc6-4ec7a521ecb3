package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.common.service.GenericService;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory;
import com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceInfo;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.util.ThriftUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimpleFeatureService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimplePredictService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftServiceInfo;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.JsonUtils;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.MDC;
import org.msgpack.core.MessageBufferPacker;
import org.msgpack.core.MessagePack;
import org.msgpack.core.MessageUnpacker;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * JavaBridge
 * Python调用Java的方法集合
 */
@Slf4j
public class JavaBridge {

    public static String sayHello(String name) {
        return "hello:" + name;
    }

    public static String predictTFModel(String modelName, String inputJson, String type) {
        long t0 = System.currentTimeMillis();
        String res = RaptorTrack.take("JavaBridge", "predictTFModel#" + modelName, () -> {
            Map<String, List<List<Number>>> input = JSONObject.parseObject(inputJson, new TypeReference<Map<String, List<List<Number>>>>() {
            }, Feature.OrderedField);
            Class aclazz = null;
            switch (type) {
                case "int": {
                    aclazz = Integer.class;
                    break;
                }
                case "long": {
                    aclazz = Long.class;
                    break;
                }
                case "float": {
                    aclazz = Float.class;
                    break;
                }
                case "double": {
                    aclazz = Double.class;
                    break;
                }
            }
            Map<String, List<?>> output = SpringContextUtils.getBean(SimplePredictService.class).predict(modelName, aclazz, input);
            return JSONObject.toJSONString(output);
        });
        log.info("JavaBridge.predictTFModel {}, cost:{}", modelName, System.currentTimeMillis() - t0);
        return res;
    }


    public static String predictWithCache(String inputJson) {
        long t0 = System.currentTimeMillis();
        if (StringUtils.isBlank(inputJson)) {
            return "";
        }
        PredictModelDto predictModelDto = JSON.parseObject(inputJson, PredictModelDto.class);
        addLog(predictModelDto.getContextData());
        String res = RaptorTrack.take("JavaBridge", "predictWithCache#" + predictModelDto.getModel_name(), () -> {
            SimplePredictService simplePredictService = SpringContextUtils.getBean(SimplePredictService.class);
            List<Map<String, Object>> result = simplePredictService.predictWithCache(predictModelDto);
            Map resultMap = Collections.singletonMap("output", result == null ? new ArrayList<>() : result);
            return JsonUtils.toJson(resultMap);
        });

        if (log.isDebugEnabled()) {
            log.debug("JavaBridge.predictWithCache# {}, cost:{}, input:{}, result={}",
                    predictModelDto.getModel_name(), System.currentTimeMillis() - t0, inputJson, res);
        }
        removeLog();
        return res;

    }

    public static String predictXGBModel(String modelName, String inputJson, String defaultValue, String contextData) {
        long t0 = System.currentTimeMillis();
        addLog(JSON.parseObject(contextData, new TypeReference<Map<String, String>>() {
        }));
        String res = RaptorTrack.take("JavaBridge", "predictXGBModel#" + modelName, () -> {
            List<Map<String, Double>> input = JSONObject.parseObject(inputJson, new TypeReference<List<Map<String, Double>>>() {
            }, Feature.OrderedField);
            List<List<Double>> output = SpringContextUtils.getBean(SimplePredictService.class).xgbPredict(modelName, input, defaultValue);
            return JSONObject.toJSONString(output);
        });
        if (log.isDebugEnabled()) {
            log.debug("JavaBridge.predictXGBModel# {}, cost:{}, input:{}, defaultValue:{}, result={}",
                    modelName, System.currentTimeMillis() - t0, inputJson, defaultValue, res);
        }
        removeLog();
        return res;
    }

    public static byte[] predictXGBByMatrix(String modelName, byte[] valueBytes, int batchSize, String contextData) {
        long t0 = System.currentTimeMillis();
        addLog(JSON.parseObject(contextData, new TypeReference<Map<String, String>>() {
        }));
        byte[] res = RaptorTrack.take("JavaBridge", "predictXGBByMatrix#" + modelName, () -> {
            MessageUnpacker unpacker = MessagePack.newDefaultUnpacker(valueBytes);
            MessageBufferPacker packer = MessagePack.newDefaultBufferPacker();
            try {
                // 反序列化input
                int length = unpacker.unpackArrayHeader();
                List<List<Double>> input = Lists.newArrayListWithCapacity(batchSize);
                int colSize = length / batchSize;
                for (int i = 0; i < batchSize; i++) {
                    input.add(new ArrayList<>(colSize));
                }
                for (int i = 0; i < length; i++) {
                    double val = unpacker.unpackDouble();
                    int x = i / colSize;
                    input.get(x).add(val);
                }
                // xgb预测
                List<List<Double>> values = SpringContextUtils.getBean(SimplePredictService.class).xgbPredictByMatrix(modelName, input);
                // 序列化xgb
                List<Double> flattenValues = values.stream().flatMap(Collection::stream).collect(Collectors.toList());
                packer.packArrayHeader(flattenValues.size());
                for (double e : flattenValues) {
                    packer.packDouble(e);
                }
                byte[] valuesBytes = packer.toByteArray();
                if (log.isDebugEnabled()) {
                    log.debug("JavaBridge.predictXGBByMatrix {}, cost:{}, input:{}, output:{}",
                            modelName, System.currentTimeMillis() - t0, JSONObject.toJSONString(input), JSONObject.toJSONString(values));
                }
                return valuesBytes;
            } catch (IOException e) {
                throw new IllegalArgumentException(String.format("predictXGBByMatrix.predictXGBByMatrix error: %s", modelName), e);
            } finally {
                try {
                    if (unpacker != null) {
                        unpacker.close();
                    }
                } catch (IOException e) {
                    log.warn("unpacker close error:", e);
                }
                try {
                    if (packer != null) {
                        packer.close();
                    }
                } catch (IOException e) {
                    log.warn("packer close error:", e);
                }
                removeLog();
            }
        });
        return res;
    }

    public static String tritonPredict(String modelName, String modeVersion, String inputValue,
                                       String inputShape, String inputType, String outputType) {
        long t0 = System.currentTimeMillis();
        String res = RaptorTrack.take("JavaBridge", "tritonPredict#" + modelName, () -> {
            LinkedHashMap<String, List<Object>> output = SpringContextUtils.getBean(SimplePredictService.class)
                    .tritonPredict(modelName, modeVersion,
                            JSONObject.parseObject(inputValue, new TypeReference<LinkedHashMap<String, List<Object>>>() {
                            }, Feature.OrderedField),
                            JSONObject.parseObject(inputShape, new TypeReference<Map<String, List<Integer>>>() {
                            }, Feature.OrderedField),
                            JSONObject.parseObject(inputType, new TypeReference<Map<String, String>>() {
                            }, Feature.OrderedField),
                            JSONObject.parseObject(outputType, new TypeReference<LinkedHashMap<String, String>>() {
                            }, Feature.OrderedField)
                    );
            return JSONObject.toJSONString(output);
        });
        log.info("JavaBridge.tritonPredict {}, cost:{}", modelName, System.currentTimeMillis() - t0);
        return res;
    }

    public static String llmPredict(String bizCode, String prompts, String extra) {
        long t0 = System.currentTimeMillis();
        String res = RaptorTrack.take("JavaBridge", "llmPredict#" + bizCode, () -> {
            Map<String, Object> output = SpringContextUtils.getBean(SimplePredictService.class)
                    .llmPredict(bizCode,
                            JSONObject.parseObject(prompts, new TypeReference<Map<String, String>>() {
                            }, Feature.OrderedField),
                            JSONObject.parseObject(extra, new TypeReference<Map<String, String>>() {
                            }, Feature.OrderedField)
                    );
            return JSONObject.toJSONString(output);
        });
        log.info("JavaBridge.llmPredict {}, cost:{}", bizCode, System.currentTimeMillis() - t0);
        return res;
    }

    public static String
    invokeThrift(String appKey, String serviceName, String methodName, String paramsJson) {
        List<String> params =
                new Gson().fromJson(paramsJson, new TypeToken<List<String>>() {
                }.getType());
        try {
            String result = ThriftUtil.invoke(appKey, serviceName, methodName, params);
            return new Gson().toJson(ImmutableMap.of("code", "0", "data", result));
        } catch (Exception e) {
            return new Gson().toJson(ImmutableMap.of("code", "1", "message", e));
        }
    }

    public static String queryFeature(String groupId, String inputJson) {
        long t0 = System.currentTimeMillis();
        String res = RaptorTrack.take("JavaBridge", "queryFeature#" + groupId, () -> {
            List<Map<String, String>> input = JSONObject.parseObject(inputJson, new TypeReference<List<Map<String, String>>>() {
            }, Feature.OrderedField);
            List<Map<String, String>> output = SpringContextUtils.getBean(SimpleFeatureService.class)
                    .query(groupId, input);
            return JSONObject.toJSONString(output);
        });
        if (log.isDebugEnabled()) {
            log.info("JavaBridge.queryFeature {}, cost:{}, input:{}, result:{}",
                    groupId, System.currentTimeMillis() - t0, inputJson, res);
        }
        return res;
    }

    public static String readLion(String key, String defaultValue) {
        long t0 = System.currentTimeMillis();
        String res = RaptorTrack.take("JavaBridge", "readLion#" + key, () -> {
            return Lion.getConfigRepository().get(key, defaultValue);
        });
        if (log.isDebugEnabled()) {
            log.info("JavaBridge.readLion, cost:{}, key:{}, defaultValue:{}, result:{}",
                    System.currentTimeMillis() - t0, key, defaultValue, res);
        }
        return res;
    }

    public static String readTair(String keysJson) {
        TairClient tairClient = SpringContextUtils.getBean(TairClient.class);
        List<String> tairKeys = JSON.parseArray(keysJson, String.class);
        Map<String, String> map = tairClient.batchGet("", tairKeys);
        return JSON.toJSONString(map);
    }

    /**
     * python上报指标
     */
    public static void catNum(String bizCode, Integer num) {
        RaptorTrack.Prediction_ResultNum.report(bizCode, 1, num);
    }

    /**
     * python上报有结果率
     */
    public static void catResultNum(String bizCode, Integer reqNum, Integer resNum) {
        RaptorTrack.Prediction_ResultNum.report(bizCode, reqNum, resNum);
    }

    /**
     * python上报log
     */
    public static void logEvent(String bizCode, String name) {
        Cat.logEvent("Python_" + bizCode, name);
    }

    /**
     * python上报error
     */
    public static void logEventError(String bizCode, String name, String json) {
        Cat.logEvent("Python_" + bizCode, name, "fail", json);
    }

    /**
     * python上报metric指标
     */
    public static void logMetric(String name, String tagsJson, Integer count) {
        Map<String, Object> tags = new HashMap<>();
        if (!StringUtils.isEmpty(tagsJson)) {
            JSONObject jsonObject = JSONObject.parseObject(tagsJson);
            tags = jsonObject.getInnerMap();
        }
        RaptorTrack.Log_Metric.report(name, tags, count);
    }

    /**
     * python日志打印
     */
    public static void print(String str) {
        log.info("JavaBridge.print {}", str);
    }

    private static void addLog(Map<String, String> contextData) {
        if (MapUtils.isNotEmpty(contextData)){
            MDC.put("defineOutput", "#DLO#{__p2jTraceId__=" + contextData.get("traceId") + "}#DLO#");
        }
    }

    private static void removeLog() {
        MDC.remove("defineOutput");
    }

    public static String pigeonInvoke(String serviceInfo, List<String> paramsType, List<String> paramValues) {
        try {
            PigeonServiceInfo pigeonServiceInfo = JSONObject.parseObject(serviceInfo, PigeonServiceInfo.class);
            GenericService pigeonProxy = SpringContextUtils.getBean(PigeonServiceFactory.class).createPigeonProxy(pigeonServiceInfo);
            String s = pigeonProxy.$invoke(pigeonServiceInfo.getMethodName(), paramsType, paramValues);
            return s;
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    public static Object thriftInvoke(String serviceInfo, List<String> paramsType, List<String> paramValues) {
        try {
            ThriftServiceInfo thriftServiceInfo = JSONObject.parseObject(serviceInfo, ThriftServiceInfo.class);
            ThriftClientProxy thriftProxy =  SpringContextUtils.getBean(ThriftClientProxyBeanConfig.class).createThriftProxy(thriftServiceInfo);
            com.meituan.service.mobile.mtthrift.generic.GenericService service = (com.meituan.service.mobile.mtthrift.generic.GenericService) thriftProxy.getObject();

            String result = service.$invoke(thriftServiceInfo.getMethodName(), paramsType, paramValues);
            log.info("thriftInvoke apply end. serviceInfo:{} result:{}", serviceInfo, result);
            return result;
        } catch (Exception e) {
            log.error("thriftInvoke error. serviceInfo:{}, paramsType:{}, paramValues:{}", serviceInfo, paramsType, paramValues, e);
        }
        return null;
    }

}
