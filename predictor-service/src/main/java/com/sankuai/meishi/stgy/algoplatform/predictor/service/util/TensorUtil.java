package com.sankuai.meishi.stgy.algoplatform.predictor.service.util;

import org.tensorflow.framework.DataType;
import org.tensorflow.framework.TensorProto;
import org.tensorflow.framework.TensorShapeProto;

import java.util.List;

import static org.tensorflow.framework.DataType.forNumber;

public class TensorUtil {
    public static TensorProto buildLongTensor(List<Long> list, int dim1, int dim2) {
        TensorProto.Builder builder = TensorProto.newBuilder();
        builder.addAllInt64Val(list);
        TensorShapeProto.Dim featuresDim1 = TensorShapeProto.Dim.newBuilder().setSize(dim1).build();
        TensorShapeProto.Dim featuresDim2 = TensorShapeProto.Dim.newBuilder().setSize(dim2).build();
        TensorShapeProto shape = TensorShapeProto.newBuilder().addDim(featuresDim1).addDim(featuresDim2).build();
        builder.setDtype(DataType.DT_INT64).setTensorShape(shape);
        return builder.build();
    }

    public static TensorProto buildIntTensor(List<Integer> list, int dim1, int dim2) {
        TensorProto.Builder builder = TensorProto.newBuilder();
        builder.addAllIntVal(list);
        TensorShapeProto.Dim featuresDim1 = TensorShapeProto.Dim.newBuilder().setSize(dim1).build();
        TensorShapeProto.Dim featuresDim2 = TensorShapeProto.Dim.newBuilder().setSize(dim2).build();
        TensorShapeProto shape = TensorShapeProto.newBuilder().addDim(featuresDim1).addDim(featuresDim2).build();
        builder.setDtype(DataType.DT_INT32).setTensorShape(shape);
        return builder.build();
    }

    public static TensorProto buildFloatTensor(List<Float> list, int dim1, int dim2) {
        TensorProto.Builder builder = TensorProto.newBuilder();
        builder.addAllFloatVal(list);
        TensorShapeProto.Dim featuresDim1 = TensorShapeProto.Dim.newBuilder().setSize(dim1).build();
        TensorShapeProto.Dim featuresDim2 = TensorShapeProto.Dim.newBuilder().setSize(dim2).build();
        TensorShapeProto shape = TensorShapeProto.newBuilder().addDim(featuresDim1).addDim(featuresDim2).build();
        builder.setDtype(DataType.DT_FLOAT).setTensorShape(shape);
        return builder.build();
    }

    public static TensorProto buildDoubleTensor(List<Double> list, int dim1, int dim2) {
        TensorProto.Builder builder = TensorProto.newBuilder();
        builder.addAllDoubleVal(list);
        TensorShapeProto.Dim featuresDim1 = TensorShapeProto.Dim.newBuilder().setSize(dim1).build();
        TensorShapeProto.Dim featuresDim2 = TensorShapeProto.Dim.newBuilder().setSize(dim2).build();
        TensorShapeProto shape = TensorShapeProto.newBuilder().addDim(featuresDim1).addDim(featuresDim2).build();
        builder.setDtype(DataType.DT_DOUBLE).setTensorShape(shape);
        return builder.build();
    }

    public static List<? extends Number> getNumber(TensorProto proto) {
        switch (forNumber(proto.getDtypeValue())) {
            case DT_INT32: {
                return proto.getIntValList();
            }
            case DT_INT64: {
                return proto.getInt64ValList();
            }
            case DT_FLOAT: {
                return proto.getFloatValList();
            }
            case DT_DOUBLE: {
                return proto.getDoubleValList();
            }
            default:
                throw new IllegalStateException("unexpected value: " + proto.getDtypeValue());
        }
    }
}
