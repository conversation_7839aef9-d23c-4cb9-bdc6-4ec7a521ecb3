package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
public class PredictContext {

    public static PredictContext newInstance(String bizCode, String abtestKey, Map<String, ?> req, Map<String, String> extra) {
        PredictContext a = new PredictContext();
        a.setBizCode(bizCode);
        a.setAbtestKey(abtestKey);
        a.setReq(req);
        a.setReqExtra(extra);
        return a;
    }

    // ----------- 请求相关 -----------
    private String bizCode;
    private String abtestKey;
    private Map<String, ?> req;
    private Map<String, String> reqExtra;
    // ----------- 返回相关 -----------
    private Map<String, ?> resp;
    private Map<String, String> respExtra = new ConcurrentHashMap<>();
    // ----------- python和java透传参数（通常用做打点） -----------
    private Map<String, String> contextData = new ConcurrentHashMap<>();

    public PredictContext setStrategy(String strategy, String distribution) {
        this.getRespExtra().put("strategy", strategy);
        this.getRespExtra().put("distribution", distribution);
        return this;
    }

    public PredictContext setCost(Long beginTimestamp) {
        this.getRespExtra().put("cost", String.valueOf(System.currentTimeMillis() - beginTimestamp));
        return this;
    }
}
