package com.sankuai.meishi.stgy.algoplatform.predictor.util;


import com.alibaba.ttl.threadpool.TtlExecutors;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;

import java.util.concurrent.*;

public class ThreadPoolFactory {
    private ThreadPoolFactory() {
    }

    static class CallerRunsPolicy implements RejectedExecutionHandler {
        private String name;

        public CallerRunsPolicy(String name) {
            this.name = name;
        }

        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            if (!e.isShutdown()) {
                RaptorTrack.Sys_ThreadPoolRejectNum.report("ThreadPoolFactory." + this.name + ".CallerRunsPolicy.run");
                r.run();
            }
        }
    }

    private final static ExecutorService pyServerLoggingThreadPool = TtlExecutors.getTtlExecutorService(
            new ThreadPoolExecutor(2, 16, 60L, TimeUnit.SECONDS,
                    new SynchronousQueue<>(), new ThreadPoolExecutor.AbortPolicy())
    );

    private final static ExecutorService profilingThreadPool = new ThreadPoolExecutor(0, 16,
            60L, TimeUnit.SECONDS,
            new SynchronousQueue<>());

    private final static ExecutorService scheduleThreadPool = Executors.newScheduledThreadPool(2);

    public static ExecutorService getPyServerLoggingThreadPool() {
        return pyServerLoggingThreadPool;
    }

    public static ExecutorService getScheduleThreadPool() {
        return scheduleThreadPool;
    }

    public static ExecutorService getProfilingThreadPool() {
        return profilingThreadPool;
    }
}
