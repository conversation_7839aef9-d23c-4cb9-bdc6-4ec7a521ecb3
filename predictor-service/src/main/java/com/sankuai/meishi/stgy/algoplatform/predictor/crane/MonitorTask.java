package com.sankuai.meishi.stgy.algoplatform.predictor.crane;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.OctoNodeMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class MonitorTask {
    @Resource
    private OctoNodeMonitorService octoNodeMonitorService;

    @Crane(Constants.APPKEY + "_tf_octo_node_monitor")
    public void serverNodeMonitor() {
        octoNodeMonitorService.serverNodeMonitor();
        log.info("server nums check done.");
    }
}
