package com.sankuai.meishi.stgy.algoplatform.predictor.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreter;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreterFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class PythonProcessTask {
    @Crane(Constants.APPKEY + "_pypyProcessRestart")
    public void pypyProcessRestart() {
        for (int i = 0; i < 10; i++) {
            String processName = "pypy3.8_common_" + i;
            PythonInterpreter p = PythonInterpreterFactory.getRuntimeByProcessName(processName);
            if (p != null) {
                try {
                    PythonInterpreterFactory.removeByProcessName(p);
                    p.destroy();
                    p.restart();
                    PythonInterpreterFactory.addProcessName(p);
                    log.info("进程{}重启成功.", processName);
                    TimeUnit.SECONDS.sleep(3);
                } catch (Exception e) {
                    log.info("进程重启失败.", e);
                }
            }
        }

    }
}
