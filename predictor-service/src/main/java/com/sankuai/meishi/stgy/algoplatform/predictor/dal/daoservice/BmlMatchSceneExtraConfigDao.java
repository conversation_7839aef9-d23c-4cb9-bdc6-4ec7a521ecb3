package com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice;

import com.meituan.mdp.boot.starter.mdpcache.anno.Cached;
import com.meituan.mdp.boot.starter.mdpcache.core.CacheType;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BmlMatchSceneExtraConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.BmlMatchSceneExtraConfigExample;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.BmlMatchSceneExtraConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> masiye
 * @date : 2025/7/8 16:49.
 * @desc : 场景额外配置Dao
 */
@Slf4j
@Repository
public class BmlMatchSceneExtraConfigDao {

    @Resource
    private BmlMatchSceneExtraConfigMapper bmlMatchSceneExtraConfigMapper;


    @Cached(area = "a1", key = "#matchSceneConfigs", cacheType = CacheType.LOCAL, timeUnit = TimeUnit.SECONDS, expire = 10, localLimit = 1000)
    public List<BmlMatchSceneExtraConfig> selectAllActiveConfigs() {
        BmlMatchSceneExtraConfigExample example = new BmlMatchSceneExtraConfigExample();
        example.createCriteria().andStatusEqualTo((byte)1);
        return bmlMatchSceneExtraConfigMapper.selectByExample(example);
    }

}
