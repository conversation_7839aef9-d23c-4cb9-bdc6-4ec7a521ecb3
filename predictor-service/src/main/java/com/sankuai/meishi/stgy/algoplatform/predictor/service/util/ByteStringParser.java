package com.sankuai.meishi.stgy.algoplatform.predictor.service.util;

import com.google.protobuf.ByteString;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * https://km.sankuai.com/collabpage/1412016719
 *
 * <AUTHOR>
 * @date 2025/4/23
 */
public class ByteStringParser {

    // 增加一个参数用来控制字节序，只对长度信息生效
    public static List<Object> extractStringsFromByteString(ByteString byteString, boolean useBigEndian) {
        List<Object> result = new ArrayList<>();
        ByteBuffer buffer = byteString.asReadOnlyByteBuffer();

        // 创建一个用于长度字段的小缓存区
        ByteBuffer lengthBuffer = ByteBuffer.allocate(4);

        // 设置长度缓存区的字节序
        if (useBigEndian) {
            lengthBuffer.order(ByteOrder.BIG_ENDIAN);
        } else {
            lengthBuffer.order(ByteOrder.LITTLE_ENDIAN);
        }

        while (buffer.remaining() > 4) {
            // 读取4字节长度字段到小缓存区
            lengthBuffer.clear();
            for (int i = 0; i < 4; i++) {
                lengthBuffer.put(buffer.get());
            }
            lengthBuffer.flip();
            int length = lengthBuffer.getInt();

            // 检查剩余的字节是否足够读取字符串
            if (buffer.remaining() >= length) {
                // 读取长度为 `length` 的字节数组
                byte[] stringBytes = new byte[length];
                buffer.get(stringBytes);
                // 将字节数组转换为字符串（使用UTF-8解码）
                String str = new String(stringBytes, StandardCharsets.UTF_8);
                result.add(str);
            } else {
                throw new IllegalArgumentException("ByteString data format is incorrect.");
            }
        }

        return result;
    }

//    public static void main(String[] args) {
//        // 构建一个示例 ByteString 使用小端字节序长度信息
//        ByteBuffer buffer = ByteBuffer.allocate(4 + 6 + 4 + 10);
//        buffer.order(ByteOrder.LITTLE_ENDIAN);  // 设置为小端字节序读写长度字段
//        buffer.putInt(6).put("Hello!".getBytes(StandardCharsets.UTF_8));
//        buffer.putInt(10).put("World!!!".getBytes(StandardCharsets.UTF_8));
//        ByteString byteString = ByteString.copyFrom(buffer.array());
//
//        // 使用小端字节序长度信息提取字符串
//        List<Object> stringList = extractStringsFromByteString(byteString, false);  // useBigEndian = false
//        System.out.println("Little Endian Length Info:");
//        for (Object str : stringList) {
//            System.out.println(str);
//        }
//
//        // 构建一个示例 ByteString 使用大端字节序长度信息
//        buffer.clear();
//        buffer.order(ByteOrder.BIG_ENDIAN);  // 设置为大端字节序读写长度字段
//        buffer.putInt(6).put("Hello!".getBytes(StandardCharsets.UTF_8));
//        buffer.putInt(10).put("World!!!".getBytes(StandardCharsets.UTF_8));
//        byteString = ByteString.copyFrom(buffer.array());
//
//        // 使用大端字节序长度信息提取字符串
//        stringList = extractStringsFromByteString(byteString, true);  // useBigEndian = true
//        System.out.println("Big Endian Length Info:");
//        for (Object str : stringList) {
//            System.out.println(str);
//        }
//    }
}
