package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import py4j.reflection.ReflectionUtil;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import static com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys.PYTHON_PROCESS_FEATURES;
import static com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys.PYTHON_PROCESS_NUM;

@Slf4j
public class PythonInterpreterFactory {
    private static final Map<String, Integer> PROCESS_NUM_CFG = JSONObject.parseObject(Lion.getConfigRepository().get(PYTHON_PROCESS_NUM, "{}"),
            new TypeReference<Map<String, Integer>>() {
            }, Feature.OrderedField);
    private static final Map<String, List<String>> FEATURES = JSONObject.parseObject(Lion.getConfigRepository().get(PYTHON_PROCESS_FEATURES, "{}"),
            new TypeReference<Map<String, List<String>>>() {
            }, Feature.OrderedField);
    private static final Map<String, List<PythonInterpreter>> runtimes = new HashMap<>();

    public static void startAll() {
        /**
         * python调用java时，类加载策略修改为自定义，防止并发调用时线程block
         */
        ReflectionUtil.setClassLoadingStrategy(new CustomThreadClassLoadingStrategy());
        initPython3Common("python3.7_common", 25333);
//        initPython3Common("python3.9_common", 25433);
        initPython3Common("python3.11_common", 25533);
//        initPython3Common("pypy3.8_common", 25533);
        log.info("PythonInterpreterFactory start all success.");
        // 后续在此注册新Python实例
    }

    private static void initPython3Common(String name, int basePort) {
        int pyProcessNum = PROCESS_NUM_CFG.getOrDefault(name, 1);
        List<String> features = FEATURES.getOrDefault(name, new ArrayList<>());
        for (int i = 0; i < pyProcessNum; i++) {
            String processName = name + "_" + i;
            PythonInterpreter commPy = new PythonInterpreter(name, processName, features);
            // 一个进程占用2个端口
            commPy.setPort(basePort + i * 2);
            commPy.run();
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.warn("initPython3Common InterruptedException");
            }

            try {
                commPy.connect();
            } catch (Exception e) {
                log.error("python connect fail, pythonName:{}", commPy.getName(), e);
                commPy.restart();
                try {
                    Thread.sleep(2000);
                } catch (Exception e1) {
                    log.warn("initPython3Common InterruptedException");
                }
                if (commPy.getGatewayServer() != null) {
                    commPy.getGatewayServer().shutdown();
                }
                commPy.connect();
            }

            runtimes.compute(name, (k, oldVal) -> {
                List<PythonInterpreter> newVal = oldVal != null ? oldVal : new ArrayList<>();
                newVal.add(commPy);
                return newVal;
            });
            log.info("{} start success.", processName);
        }
    }

    public static PythonInterpreter getRuntime(String name) {
        List<PythonInterpreter> r = runtimes.get(name);
        Preconditions.checkNotNull(r, "Interpreter不存在:" + name);
        Preconditions.checkState(!r.isEmpty(), "Interpreter不存在(无实例):" + name);
        if (r.size() == 1) {
            return r.get(0);
        }
        // 负载均衡：随机策略
        int idx = ThreadLocalRandom.current().nextInt(r.size());
        PythonInterpreter pythonInterpreter = r.get(idx);
        log.info("getRuntime name={}", pythonInterpreter.getProcessName());
        return pythonInterpreter;

    }

    public static List<PythonInterpreter> getAll() {
        return runtimes.values().stream()
                .flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 根据进程名称获取 PythonInterpreter 实例
     *
     * @param processName 进程名称
     * @return 符合进程名称的 PythonInterpreter 实例，如果不存在则返回 null
     */
    public static PythonInterpreter getRuntimeByProcessName(String processName) {
        return getAll().stream().filter(f -> f.getProcessName().equals(processName)).findFirst().orElse(null);
    }

    public static void removeByProcessName(PythonInterpreter pythonInterpreter) {
        List<PythonInterpreter> list = runtimes.get(pythonInterpreter.getName());
        if (!CollectionUtils.isEmpty(list)) {
            list.removeIf(interpreter -> interpreter.getProcessName().equals(pythonInterpreter.getProcessName()));
            log.info("python进程摘除成功");
        }
    }

    public static void addProcessName(PythonInterpreter pythonInterpreter) {
        runtimes.compute(pythonInterpreter.getName(), (k, oldVal) -> {
            List<PythonInterpreter> newVal = oldVal != null ? oldVal : new ArrayList<>();
            newVal.add(pythonInterpreter);
            return newVal;
        });
    }
}
