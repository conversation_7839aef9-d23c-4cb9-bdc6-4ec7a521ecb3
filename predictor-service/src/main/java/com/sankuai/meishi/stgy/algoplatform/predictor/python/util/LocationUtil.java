package com.sankuai.meishi.stgy.algoplatform.predictor.python.util;

import org.springframework.util.ResourceUtils;

import java.io.FileNotFoundException;

public class LocationUtil {
    public static String getRuntimePath() {
        try {
            String currentPath = ResourceUtils.getURL("classpath:").getPath();
            if (currentPath.contains(".jar!")) {
                // springboot in server
                // file:/docker/opt/meituan/APPKEY/MODULE_NAME.jar!/BOOT-INF/classes!/
                String tmp = currentPath;
                tmp = tmp.substring(5);
                tmp = tmp.substring(0, tmp.indexOf(".jar!"));
                currentPath = tmp.substring(0, tmp.lastIndexOf("/") + 1);
                return currentPath + "classes";
            }
            // 单元测试场景
            if (currentPath.contains("test-classes")) {
                currentPath = currentPath.replace("test-classes", "classes");
                return currentPath;
            }
            // 本地调试
            if (currentPath.contains("User")) {
                // local
                return currentPath;
            }
            throw new IllegalStateException("找不到合适的路径");
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
    }
}
