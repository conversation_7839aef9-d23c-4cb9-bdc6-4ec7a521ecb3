package com.sankuai.meishi.stgy.algoplatform.predictor.s3;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.File;

/**
 * <AUTHOR>
 * @date 2024年03月27日 6:08 下午
 */

@Slf4j
@Service
public class AmazonS3Service {
    public void getObject(AmazonS3 s3Client, String bucketName, String objectName , File file){
        try {
            s3Client.getObject(new GetObjectRequest(bucketName, objectName), file);
        } catch (AmazonClientException e) {
            Cat.logError(e);
        }
    }

    public File getFile(AmazonS3 s3Client, String bucketName, String objectName) {
        if (objectName == null) {
            return null;
        }
        File file = new File(objectName);
        getObject(s3Client, bucketName, objectName, file);
        return file;
    }
}
