package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import java.util.function.BiFunction;
import java.util.function.Function;

public class RetryUtil {

    public static <R> R retry(Supplier<R> supplier, BiFunction<R, Exception, Boolean> getNeedRetry,
                              int maxAttempts, long delay) {
        Exception ex = null;
        R res = null;
        for (int i = 0; i < maxAttempts; i++) {
            // 执行
            try {
                res = supplier.get();
                ex = null;
            } catch (Exception e) {
                ex = e;
                res = null;
            }
            // 是否需要重试
            boolean needRetry = getNeedRetry.apply(res, ex);
            if (!needRetry) {
                return res;
            }
            // 重试时长(最后一次不等待)
            if (i < maxAttempts - 1) {
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        if (ex != null) {
            throw new RuntimeException(ex);
        }
        return res;
    }

    public static void retry(Runnable runnable, Function<Exception, Boolean> getNeedRetry,
                             int maxAttempts, long delay) {
        Exception ex = null;
        for (int i = 0; i < maxAttempts; i++) {
            // 执行
            try {
                runnable.run();
                ex = null;
            } catch (Exception e) {
                ex = e;
            }
            // 是否需要重试
            boolean needRetry = getNeedRetry.apply(ex);
            if (!needRetry) {
                return;
            }
            // 重试时长(最后一次不等待)
            if (i < maxAttempts - 1) {
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        if (ex != null) {
            throw new RuntimeException(ex);
        }
    }

    @FunctionalInterface
    public interface Supplier<T> {
        T get() throws Exception;
    }

    @FunctionalInterface
    public interface Runnable {
        void run() throws Exception;
    }
}
