package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfigListener;
import com.meituan.mdp.boot.starter.config.vo.ConfigEvent;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.ModelCacheConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.PredictCacheConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LionConfigService {
    @MdpConfigListener(LionKeys.PREDICT_CACHE_SWITCH)
    public void listenerCacheSwitch(ConfigEvent configEvent) {
        log.info("LionConfigService.listenerCacheSwitch configEvent：{}", JSON.toJSONString(configEvent));
        if (!MdpContextUtils.isProdEnv()) {
            return;
        }
        if (StringUtils.equals(configEvent.getNewValue(), "false")) {
            log.error("一级缓存未开启");
            Cat.logEvent("CacheConfigError", LionKeys.PREDICT_CACHE_SWITCH);
        }
    }

    @MdpConfigListener(LionKeys.PREDICT_CACHE_CONFIG)
    public void listenerCacheSConfig(ConfigEvent configEvent) {
        log.info("LionConfigService.listenerCacheSConfig configEvent：{}", JSON.toJSONString(configEvent));
        if (!MdpContextUtils.isProdEnv()) {
            return;
        }
        List<PredictCacheConfig> list = JSON.parseObject(configEvent.getNewValue(), new TypeReference<List<PredictCacheConfig>>() {
        });
        if (CollectionUtils.isEmpty(list)) {
            log.error("一级缓存未配置");
            Cat.logEvent("CacheConfigError", LionKeys.PREDICT_CACHE_CONFIG + "#null");
            return;
        }

        List<String> needMonitorBizCodeList = Lion.getConfigRepository().getList(LionKeys.NEED_MONITOR_CACHE_BIZCODE_LIST);
        if (CollectionUtils.isEmpty(needMonitorBizCodeList)) {
            return;
        }
        List<String> bizCodes = list.stream().map(PredictCacheConfig::getBizCode).collect(Collectors.toList());
        for (String bizCode : needMonitorBizCodeList) {
            if (!bizCodes.contains(bizCode)) {
                log.error("一级缓存未配置:{}", bizCode);
                Cat.logEvent("CacheConfigError", LionKeys.PREDICT_CACHE_CONFIG + "#" + bizCode);
            }
        }
    }

    @MdpConfigListener(LionKeys.PREDICT_MODEL_CACHE_CONFIG)
    public void listenerPredictCacheSConfig(ConfigEvent configEvent) {
        log.info("LionConfigService.listenerPredictCacheSConfig configEvent：{}", JSON.toJSONString(configEvent));
        if (!MdpContextUtils.isProdEnv()) {
            return;
        }
        ModelCacheConfig modelCacheConfig = JSON.parseObject(configEvent.getNewValue(), ModelCacheConfig.class);
        if (!modelCacheConfig.getGlobalCacheSwitch()) {
            log.error("二级缓存未开启");
            Cat.logEvent("CacheConfigError", LionKeys.PREDICT_MODEL_CACHE_CONFIG + "#globalCacheSwitch");
        }
        if (!modelCacheConfig.getCoupleCacheSwitch()) {
            log.error("特征缓存未开启");
            Cat.logEvent("CacheConfigError", LionKeys.PREDICT_MODEL_CACHE_CONFIG + "#coupleCacheSwitch");
        }
        if (!modelCacheConfig.getModelPredictCacheSwitch()) {
            log.error("四级缓存未开启");
            Cat.logEvent("CacheConfigError", LionKeys.PREDICT_MODEL_CACHE_CONFIG + "#modelPredictCacheSwitch");
        }
    }

}
