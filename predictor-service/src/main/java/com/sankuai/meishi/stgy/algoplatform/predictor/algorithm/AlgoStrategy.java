package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * 算法策略（单策略或策略编排）
 */
@Getter
public class AlgoStrategy {
    private Long id;
    private AlgoPackage algoPackage;
    private String entrancePath;
    private String entranceMethod;

    public AlgoStrategy(Long id, AlgoPackage algoPackage, String entrancePath, String entranceMethod) {
        this.id = id;
        this.algoPackage = Objects.requireNonNull(algoPackage);
        this.entrancePath = entrancePath;
        this.entranceMethod = entranceMethod;
    }

    public Map<String, ?> run(Map<String, ?> req, Map<String, String> contextData) {
        // 当前只支持单策略
        return algoPackage.invoke(this.getEntrancePath(), this.getEntranceMethod(), req, contextData);
    }
}
