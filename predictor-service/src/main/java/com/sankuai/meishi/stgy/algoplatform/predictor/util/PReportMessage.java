package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.sankuai.nib.data.zb.datatrace.platform.sdk.dto.ExtendData;
import com.sankuai.nib.data.zb.datatrace.platform.sdk.enums.BuEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class PReportMessage {
    /**
     * traceID
     */
    private String traceId;
    /**
     * 上报事件
     */
    private String eventDesc;
    /**
     * 入参
     */
    private Object req;
    /**
     * 出参
     */
    private String resp;
    /**
     * 额外信息
     */
    private Map<String, Object> customData;
    /**
     * 唯一key
     */
    private String businessId;
    /**
     * 需要结构化的额外信息
     */
    List<ExtendData> extendDataList;

    private String mtPoiId;
    private String mtDealId;
    private String djPoiId;
    private String djDealId;
    private boolean success;
    /**
     * BU信息
     */
    private BuEnum buEnum;

    public PReportMessage() {
    }

    public PReportMessage(String traceId, String eventDesc) {
        this.traceId = traceId;
        this.eventDesc = eventDesc;
    }
}
