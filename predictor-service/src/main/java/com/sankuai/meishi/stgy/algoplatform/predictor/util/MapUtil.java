package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.apache.commons.collections4.MapUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/1/9
 */
public class MapUtil {

    /**
     * 拆分Map
     *
     * @param map  数据
     * @param size 每批大小
     * @return
     */
    public static <K, V> List<Map<K, V>> partition(Map<K, V> map, int size) {
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyList();
        }
        if (size <= 0 || map.size() <= size) {
            return Collections.singletonList(map);
        }

        int initialCapacity = (map.size() % size == 0) ?
                map.size() / size : (map.size() / size + 1);
        List<Map<K, V>> resultList = new ArrayList<>(initialCapacity);
        Map<K, V> tempMap = new LinkedHashMap<>(size);

        for (Map.Entry<K, V> entry : map.entrySet()) {
            tempMap.put(entry.getKey(), entry.getValue());

            if (tempMap.size() == size) {
                resultList.add(tempMap);
                tempMap = new LinkedHashMap<>(size);
            }
        }

        if (!tempMap.isEmpty()) {
            resultList.add(tempMap);
        }

        return resultList;
    }
}
