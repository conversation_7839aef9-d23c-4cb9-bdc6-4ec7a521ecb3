package com.sankuai.meishi.stgy.algoplatform.predictor.config;

import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.cat.servlet.CatFilter;
import com.dianping.lion.client.Lion;
import com.meituan.data.feature.thrift.FeatureService;
import com.meituan.hadoop.afo.serving.common.OnlineService;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.bean.MafkaConsumer;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meishi.stgy.algoplatform.predictor.mq.BmlDealTagListener;
import com.sankuai.meishi.stgy.algoplatform.predictor.mq.DzPredictListener;
import com.sankuai.meishi.stgy.algoplatform.predictor.mq.PredictListener;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreterFactory;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.util.GitUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.ThreadPoolFactory;
import com.sankuai.meituan.config.MtConfigClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import static com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants.TF_MODEL_PREDICT_TIMEOUT;
import static com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants.TF_MODEL_SERVER_APPKEY;

@Slf4j
@Configuration
@CraneConfiguration
@EnableScheduling
@ComponentScan("com.sankuai.service.wpt.horizon")
public class BeanConfig implements SchedulingConfigurer {

    @Autowired
    private MafkaProperties mafkaProperties;
    @MdpConfig(LionKeys.PREDICT_LISTENER_SET_ENVIRONMENT_FLAG)
    private Boolean setEnvironmentFlag = false;

    private final String ENV_DEFAULT = "master";
    private final String ENV_BIG_REQUEST = "big_request";
    private final String ENV_DAPAN_CATE = "dapan_cate";
    private final String ENV_FAST_FOOD = "fast_food";
    private final String ENV_BML_DEAL_TAG_FRESH = "bml_deal_tag_fresh";
    private final String ENV_DAO_ZONG = "daozong_bml";

    static {
        // 在服务启动时就初始化GitUtil
        GitUtil.init();
    }

    @Bean
    public String pythonRuntimeStatus() {
        PythonInterpreterFactory.startAll();
        return "ok";
    }

    @Bean(destroyMethod = "destroy")
    public ThriftClientProxy afoClientProxy() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey(TF_MODEL_SERVER_APPKEY);
        proxy.setServiceInterface(Class.forName("com.meituan.hadoop.afo.serving.common.OnlineService"));
        proxy.setNettyIO(true);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(TF_MODEL_PREDICT_TIMEOUT);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public OnlineService.Iface afoClient(@Qualifier("afoClientProxy") ThriftClientProxy afoClientProxy) throws Exception {
        return (OnlineService.Iface) afoClientProxy.getObject();
    }

    @Bean(destroyMethod = "destroy")
    public ThriftClientProxy featureClientProxy() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.persona.feature");
        proxy.setServiceInterface(Class.forName("com.meituan.data.feature.thrift.FeatureService"));
        proxy.setNettyIO(true);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(500);
        // test环境测试
//        proxy.setServerIpPorts("***********:8412");
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public FeatureService.Iface featureClient(@Qualifier("featureClientProxy") ThriftClientProxy featureClientProxy) throws Exception {
        return (FeatureService.Iface) featureClientProxy.getObject();
    }

    /**
     * 接口文档：https://km.sankuai.com/page/151393191
     */
    @Bean(destroyMethod = "destroy")
    public ThriftClientProxy horizonThriftClient() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.wpt.horizon.horizonserver");
        proxy.setServiceInterface(Class.forName("com.meituan.service.wpt.horizon.server.api.HorizonCacheService"));
        proxy.setNettyIO(true);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(2000);
        proxy.setServerDynamicWeight(true);
        proxy.setClusterManager("octo");
//        proxy.setRemoteServerPort(10408); serverPort和setFilterByServiceName 冲突，建议去掉此配置
        //https://km.sankuai.com/page/1330002794
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean(initMethod = "init")
    public MtConfigClient horizonMcc() {
        MtConfigClient client = new MtConfigClient();
        client.setId("id");
        client.setAppkey("com.sankuai.wpt.horizon.horizonserver");
        client.setModel("v2");
        return client;
    }

    /**
     * EnableScheduling 对应的线程池配置
     *
     * @param taskRegistrar taskRegistrar
     */
    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(ThreadPoolFactory.getScheduleThreadPool());
    }

    /**
     * Raptor URL打点
     */
    @Bean
    public FilterRegistrationBean catFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        CatFilter filter = new CatFilter();
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("cat-filter");
        registration.setOrder(1);
        return registration;
    }

    private void setMafkaConsumerCell(MafkaConsumer mafkaConsumer, String topic) {
        Map<String, String> consumerConfig = Lion.getConfigRepository().getMap(LionKeys.MAFKA_CONSUMER_CONFIG, String.class);
        String setEnv = consumerConfig.get(topic);

        if (ENV_BIG_REQUEST.equals(setEnv)) {
            mafkaConsumer.setCell(mafkaProperties.getBigRequestSet());
            Map<String, String> otherProperties = new HashMap<>(2);
            otherProperties.put(ConsumerConstants.CELL_NAME, mafkaProperties.getBigRequestSet());
            mafkaConsumer.setOtherProperties(otherProperties);
        } else if (ENV_DAPAN_CATE.equals(setEnv)) {
            mafkaConsumer.setCell(mafkaProperties.getDapanCateSet());
            Map<String, String> otherProperties = new HashMap<>(2);
            otherProperties.put(ConsumerConstants.CELL_NAME, mafkaProperties.getDapanCateSet());
            mafkaConsumer.setOtherProperties(otherProperties);
        } else if (ENV_FAST_FOOD.equals(setEnv)) {
            mafkaConsumer.setCell(mafkaProperties.getFastFoodSet());
            Map<String, String> otherProperties = new HashMap<>(2);
            otherProperties.put(ConsumerConstants.CELL_NAME, mafkaProperties.getFastFoodSet());
            mafkaConsumer.setOtherProperties(otherProperties);
        } else if (ENV_BML_DEAL_TAG_FRESH.equals(setEnv)) {
            mafkaConsumer.setCell(mafkaProperties.getBmlDealTagFreshSet());
            Map<String, String> otherProperties = new HashMap<>(2);
            otherProperties.put(ConsumerConstants.CELL_NAME, mafkaProperties.getBmlDealTagFreshSet());
            mafkaConsumer.setOtherProperties(otherProperties);
        } else if (ENV_DAO_ZONG.equals(setEnv)) {
            mafkaConsumer.setCell(mafkaProperties.getDzMatchSet());
            Map<String, String> otherProperties = new HashMap<>(2);
            otherProperties.put(ConsumerConstants.CELL_NAME, mafkaProperties.getDzMatchSet());
            mafkaConsumer.setOtherProperties(otherProperties);
        }
        if (setEnv.startsWith("gray-release-")) {
            mafkaConsumer.setCell(setEnv);
            Map<String, String> otherProperties = new HashMap<>(2);
            otherProperties.put(ConsumerConstants.CELL_NAME, setEnv);
            mafkaConsumer.setOtherProperties(otherProperties);
        }
        log.info("MafkaConsumer topic: {}, setEnv: {}", topic, setEnv);
    }

    @Bean(initMethod = "start")
    @ConditionalOnExpression("#{T(com.sankuai.meishi.stgy.algoplatform.predictor.config.MafkaHelper).isConsumer('${mafka.predictListenerTopic}')}")
    public MafkaConsumer predictListenerConsumer(@Qualifier("predictListener") PredictListener predictResultConsumer) {
        MafkaConsumer mafkaConsumer = new MafkaConsumer();

        mafkaConsumer.setNamespace(mafkaProperties.getNamespace());
        mafkaConsumer.setAppkey(mafkaProperties.getAppKey());
        mafkaConsumer.setTopic(mafkaProperties.getPredictListenerTopic());
        mafkaConsumer.setGroup(mafkaProperties.getPredictGroup());

        setMafkaConsumerCell(mafkaConsumer, mafkaProperties.getPredictListenerTopic());

//        mafkaConsumer.setConsumerType("CommonConsumer"); // Push模式：平滑消费

        mafkaConsumer.setClassName("java.lang.String");
        mafkaConsumer.setListener(predictResultConsumer);

        return mafkaConsumer;
    }

    @Bean(initMethod = "start")
    @ConditionalOnExpression("#{T(com.sankuai.meishi.stgy.algoplatform.predictor.config.MafkaHelper).isConsumer('${mafka.bmlDealTagListenerTopic}')}")
    public MafkaConsumer bmlDealTagListenerConsumer(@Qualifier("bmlDealTagListener") BmlDealTagListener bmlDealTagListener) {
        MafkaConsumer mafkaConsumer = new MafkaConsumer();

        mafkaConsumer.setNamespace(mafkaProperties.getNamespace());
        mafkaConsumer.setAppkey(mafkaProperties.getAppKey());
        mafkaConsumer.setTopic(mafkaProperties.getBmlDealTagListenerTopic());
        mafkaConsumer.setGroup(mafkaProperties.getBmlDealTagGroup());

        setMafkaConsumerCell(mafkaConsumer, mafkaProperties.getBmlDealTagListenerTopic());

        mafkaConsumer.setClassName("java.lang.String");
        mafkaConsumer.setListener(bmlDealTagListener);
        return mafkaConsumer;
    }

    @Bean(initMethod = "start")
    @ConditionalOnExpression("#{T(com.sankuai.meishi.stgy.algoplatform.predictor.config.MafkaHelper).isConsumer('${mafka.dzPredictListenerTopic}')}")
    public MafkaConsumer dzPredictListenerConsumer(@Qualifier("dzPredictListener") DzPredictListener predictResultConsumer) {
        MafkaConsumer mafkaConsumer = new MafkaConsumer();

        mafkaConsumer.setNamespace(mafkaProperties.getDzNamespace());
        mafkaConsumer.setAppkey(mafkaProperties.getAppKey());
        mafkaConsumer.setTopic(mafkaProperties.getDzPredictListenerTopic());
        mafkaConsumer.setGroup(mafkaProperties.getDzSubscribeGroup());

        setMafkaConsumerCell(mafkaConsumer, mafkaProperties.getDzPredictListenerTopic());

        mafkaConsumer.setClassName("java.lang.String");
        mafkaConsumer.setListener(predictResultConsumer);
        return mafkaConsumer;
    }

    @Bean(name = "predictResultProducer")
    public IProducerProcessor predictResultProduceProducer() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, mafkaProperties.getNamespace());
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, mafkaProperties.getAppKey());
        IProducerProcessor<Object, Object> objectObjectIProducerProcessor = MafkaClient.buildProduceFactory(properties, mafkaProperties.getPredictResultTopic());
        return objectObjectIProducerProcessor;
    }

    @Bean(name = "bmlDealTagResultProducer")
    public IProducerProcessor bmlDealTagResultProducerProcessor() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, mafkaProperties.getNamespace());
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, mafkaProperties.getAppKey());
        IProducerProcessor<Object, Object> objectObjectIProducerProcessor = MafkaClient.buildProduceFactory(properties, mafkaProperties.getBmlDealTagResultTopic());
        return objectObjectIProducerProcessor;
    }



    @Bean(name = "dzPredictorResult")
    public IProducerProcessor dzPredictResultProduceProducer() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, mafkaProperties.getDzNamespace());
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, mafkaProperties.getAppKey());
        IProducerProcessor<Object, Object> objectObjectIProducerProcessor = MafkaClient.buildProduceFactory(properties, mafkaProperties.getDzPredictResultTopic());
        return objectObjectIProducerProcessor;
    }
}
