package com.sankuai.meishi.stgy.algoplatform.predictor.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/6/28 10:33
 **/
@ConfigurationProperties(prefix = "mafka")
@Component
@Data
public class MafkaProperties {

    private String namespace;

    private String appKey;

    private String predictGroup;

    private String predictListenerTopic;

    private String predictResultTopic;

    private String bigRequestSet;

    private String dapanCateSet;

    private String fastFoodSet;

    private String bmlDealTagListenerTopic;

    private String bmlDealTagGroup;

    private String bmlDealTagResultTopic;

    private String bmlDealTagFreshSet;

    private String dzNamespace;

    private String dzPredictListenerTopic;

    private String dzSubscribeGroup;

    private String dzPredictResultTopic;

    private String dzMatchSet;

}
