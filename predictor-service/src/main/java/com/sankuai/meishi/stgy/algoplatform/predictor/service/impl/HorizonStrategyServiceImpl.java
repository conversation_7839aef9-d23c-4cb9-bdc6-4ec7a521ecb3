package com.sankuai.meishi.stgy.algoplatform.predictor.service.impl;

import com.sankuai.meishi.stgy.algoplatform.predictor.service.HorizonStrategyService;
import com.sankuai.service.wpt.horizon.core.IHorizonClient;
import com.sankuai.service.wpt.horizon.core.expection.HorizonException;
import com.sankuai.service.wpt.horizon.core.vo.HorizonStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class HorizonStrategyServiceImpl implements HorizonStrategyService {
    @Resource
    private IHorizonClient horizonClient;

    @Override
    public String getStrategyByLayerId(String expKey, String abKey, int layerId, String group, Map<String, String> extra) {
        long t0 = System.currentTimeMillis();
        String strategyKey = "default";
        try {
            List<HorizonStrategy> strategies = Optional.ofNullable(horizonClient.getStrategyByLayerIdV2(abKey, extra, group, layerId))
                    .orElse(new ArrayList<>());
            if (log.isDebugEnabled()) {
                log.debug("horizonClient.getStrategyByLayerIdV2: {}", strategies);
            }
            for (HorizonStrategy strategy : strategies) {
                if (expKey.equals(strategy.getExpKey())) {
                    strategyKey = Optional.ofNullable(strategy.getStrategyKey())
                            .orElse("default");
                    break;
                }
            }
        } catch (HorizonException e) {
            log.error("getStrategyByLayerId error: expKey:{}, abKey:{}, group:{}, layerId:{}, extra:{}, ex:", expKey, abKey, group, layerId, extra, e);
        }
        log.info("getStrategyByLayerId: req: expKey:{}, abKey:{}, group:{}, layerId:{}, extra:{}, resp: strategyKey:{}, cost:{}",
                expKey, abKey, group, layerId, extra, strategyKey, System.currentTimeMillis() - t0);
        return strategyKey;
    }

    @Override
    public String getStrategyByLayerId(String expKey, String abKey, int layerId) {
        return getStrategyByLayerId(expKey, abKey, layerId, "group", new HashMap<>());
    }
}
