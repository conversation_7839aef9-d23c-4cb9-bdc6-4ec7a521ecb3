package com.sankuai.meishi.stgy.algoplatform.predictor.thrift;


import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.common.domain.GenericType;
import com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.Objects;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/10
 */
@Slf4j
@Configuration
public class ThriftClientProxyBeanConfig {

    public static Map<String, ThriftClientProxy> serviceClient = Maps.newConcurrentMap();

    public ThriftClientProxy createThriftProxy(ThriftServiceInfo thriftServiceInfo) throws Exception {
        String thriftProxyKey = getThriftProxyKey(thriftServiceInfo);
        return serviceClient.computeIfAbsent(thriftProxyKey, key -> {
            try {
                ThriftClientProxy clientProxy = new ThriftClientProxy();
                clientProxy.setGeneric(GenericType.JSON_SIMPLE.getName());
                clientProxy.setAppKey("com.sankuai.algoplatform.predictor");
                clientProxy.setTimeout(thriftServiceInfo.getTimeOut() == null ? 6000 : thriftServiceInfo.getTimeOut());
                clientProxy.setGenericServiceName(thriftServiceInfo.getInterfaceName());

                if (thriftServiceInfo.getAppKey() != null) {
                    clientProxy.setRemoteAppkey(thriftServiceInfo.getAppKey());
                    if (!StringUtils.isBlank(thriftServiceInfo.getPort())) {
                        clientProxy.setRemoteServerPort(Integer.parseInt(thriftServiceInfo.getPort()));
                    } else {
                        clientProxy.setFilterByServiceName(true);
                    }
                } else if (!StringUtils.isBlank(thriftServiceInfo.getIp()) && !StringUtils.isBlank(thriftServiceInfo.getPort())) {
                    clientProxy.setRemoteUniProto(true);
                    clientProxy.setServerIpPorts(thriftServiceInfo.getIp() + ":" + thriftServiceInfo.getPort());
                }
                clientProxy.setRemoteUniProto(true);
                clientProxy.afterPropertiesSet();
                return clientProxy;
            } catch (Exception e) {
                log.error("Failed to create ThriftClientProxy for key: {}", key, e);
                throw new RuntimeException("Failed to create ThriftClientProxy", e);
            }
        });
    }

    public void destroy(String proxyKey) {
        ThriftClientProxy clientProxy = serviceClient.remove(proxyKey);
        if (Objects.isNull(clientProxy)) {
            return;
        }
        try {
            clientProxy.destroy();
        } catch (Exception e) {
            log.error("GeneralizeClient.destroy clientProxy:{}", clientProxy, e);
        }
    }

    @PreDestroy
    private void destroyAll() {
        if (MapUtils.isEmpty(serviceClient)) {
            return;
        }
        for (Map.Entry<String, ThriftClientProxy> entry : serviceClient.entrySet()) {
            destroy(entry.getKey());
            log.info("destroy thrift proxy, key:{}", entry.getKey());
        }
    }

    private String getThriftProxyKey(ThriftServiceInfo thriftServiceInfo) {
        return thriftServiceInfo.getAppKey() + "_" + thriftServiceInfo.getInterfaceName() + "_" + thriftServiceInfo.getCell() + "_" + thriftServiceInfo.getIp() + "_" + thriftServiceInfo.getPort()+"_"+ thriftServiceInfo.getTimeOut();
    }

    public void destroy(ThriftServiceInfo thriftServiceInfo) {
        String proxyKey = getThriftProxyKey(thriftServiceInfo);
        ThriftClientProxy clientProxy = serviceClient.remove(proxyKey);
        if (Objects.isNull(clientProxy)) {
            return;
        }
        try {
            clientProxy.destroy();
        } catch (Exception e) {
            log.error("GeneralizeClient.destroy clientProxy:{}", clientProxy, e);
        }
    }
}
