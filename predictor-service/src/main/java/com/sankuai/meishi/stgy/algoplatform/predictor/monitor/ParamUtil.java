package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

public class ParamUtil {
    public static Object getFieldValueByName(String fieldName, Method method, Object[] args) {
        int index = Integer.MAX_VALUE;
        Parameter[] parameters = method.getParameters();
        if (parameters != null) {
            for (int i = 0; i < parameters.length; i++) {
                Parameter parameter = parameters[i];
                if (fieldName.equals(parameter.getName())) {
                    index = i;
                    break;
                }
            }
        }
        if (args != null && index < args.length) {
            return args[index];
        }
        throw new IllegalStateException("NoSuchFieldName: " + fieldName);
    }
}
