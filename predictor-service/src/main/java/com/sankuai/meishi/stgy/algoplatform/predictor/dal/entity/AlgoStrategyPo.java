package com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity;

import lombok.*;

import java.util.Date;

/**
 * 表名: algo_strategy
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AlgoStrategyPo {
    /**
     * 字段: id
     * 说明: 自增主键
     */
    private Long id;

    /**
     * 字段: note
     * 说明: 算法包说明
     */
    private String note;

    /**
     * 字段: package_id
     * 说明: 算法包id
     */
    private Long packageId;

    /**
     * 字段: entrance_path
     * 说明: 算法包入口路径
     */
    private String entrancePath;

    /**
     * 字段: entrance_method
     * 说明: 算法包入口函数
     */
    private String entranceMethod;

    /**
     * 字段: status
     * 说明: 状态，0：正常，-1：删除
     */
    private Integer status;

    /**
     * 字段: add_time
     * 说明: 添加时间
     */
    private Date addTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    private Date updateTime;
}