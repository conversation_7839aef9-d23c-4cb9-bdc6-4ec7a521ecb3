package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import groovy.lang.Binding;
import groovy.lang.GroovyClassLoader;
import groovy.lang.Script;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

public class GroovyScriptFactory {
    private static Cache<String, Class<Script>> innerLruCache = CacheBuilder.newBuilder()
            .maximumSize(5000) //最大容量
            .expireAfterAccess(3, TimeUnit.DAYS) //缓存过期时长
            .concurrencyLevel(Runtime.getRuntime().availableProcessors())// 设置并发级别为cpu核心数
            .build();

    public static Class<Script> getScriptClass(String script) {
        String scriptKey = DigestUtils.md5Hex(script).toLowerCase();
        try {
            return innerLruCache.get(scriptKey, () -> {
                GroovyClassLoader groovyLoader = new GroovyClassLoader();
                Class<Script> groovyClass = (Class<Script>) groovyLoader.parseClass(script);
                RaptorTrack.GroovyScript_Loaded.report(groovyClass.getName());
                return groovyClass;
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(String.format("init groovy script error: %s, ex", script), e);
        }
    }

    public static Object runScript(String script, Map<String, Object> bindings) {
        Binding binding = new Binding();
        if (MapUtils.isNotEmpty(bindings)) {
            bindings.forEach(binding::setVariable);
        }
        Class<Script> scriptClass = GroovyScriptFactory.getScriptClass(script);
        Script instance;
        try {
            instance = scriptClass.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(String.format("runScript.newInstance error: %s, ex:", script), e);
        }
        instance.setBinding(binding);
        return instance.run();
    }
}
