package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import io.opencensus.trace.TraceId;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

public class RaptorTrack {
    /**
     * 异常逻辑
     */
    public static Item Sys_UnexpectedVisitNum = params -> logEvent("Sys_UnexpectedVisitNum", params);

    /**
     * 内部错误
     */
    public static Item Sys_InternalErrorNum = params -> logEvent("Sys_InternalErrorNum", params);

    /**
     * 线程池任务溢出
     */
    public static Item Sys_ThreadPoolRejectNum = params -> logEvent("Sys_ThreadPoolRejectNum", params);
    /**
     * Git拉取状态
     */
    public static Item Git_Checkout = params -> logEvent("Git_Checkout", params);
    /**
     * Python进程死亡
     */
    public static Item Python_DeadProcess = params -> logEvent("Python_DeadProcess", params);
    /**
     * Groovy脚本加载
     */
    public static Item GroovyScript_Loaded = params -> logEvent("GroovyScript_Loaded", params);
    /**
     * 限制条件实时化标签计算
     */
    public static Item HANDLE_BML_DEAL_TAG = params -> logEvent("handleBmlDealTag", params);
    /**
     * 输入为空
     */
    public static Item PREDICT_INPUT_EMPTY = params -> logEvent("predictInputIsEmpty", params);

    /**
     * 预计算查询有结果上报
     * params[0]:bizCode
     * params[1]:请求数量
     * params[2]:结果数量
     */
    public static Item Prediction_ResultNum = params -> {
        String bizCode = (String) params[0];
        int reqNum = ((Number) params[1]).intValue();
        int resultNum = ((Number) params[2]).intValue();

        MetricHelper helper = MetricHelper.build().name("Prediction_ResultNum")
                .tag("bizCode", bizCode)
                .tag("tag", "req");
        helper.count(reqNum);

        helper = MetricHelper.build().name("Prediction_ResultNum")
                .tag("bizCode", bizCode)
                .tag("tag", "result");
        helper.count(resultNum);
    };

    public static Item PREDICT_CACHE_NUM = params -> {
        String bizCode = (String) params[0];
        String useCache = (String) params[1];

        MetricHelper helper = MetricHelper.build().name("predict_cache_num")
                .tag("bizCode", bizCode)
                .tag("useCache", useCache);
        if (params.length >= 3) {
            Map<String, String> tmpMap = (Map<String, String>) params[2];
            if (MapUtils.isNotEmpty(tmpMap)) {
                Map<String, String> bizTags = new HashMap<>(tmpMap);
                bizTags.remove("traceId");
                bizTags.forEach(helper::tag);
            }
        }
        helper.count();
    };

    public static Item MODEL_GLOBAL_CACHE_NUM = params -> {
        String modelName = (String) params[0];
        String useCache = (String) params[1];

        MetricHelper helper = MetricHelper.build().name("model_global_cache_num")
                .tag("model", modelName)
                .tag("useCache", useCache);
        if (params.length >= 3) {
            Map<String, String> tmpMap = (Map<String, String>) params[2];
            if (MapUtils.isNotEmpty(tmpMap)) {
                Map<String, String> bizTags = new HashMap<>(tmpMap);
                bizTags.remove("traceId");
                bizTags.forEach(helper::tag);
            }
        }
        helper.count();

    };

    public static Item MODEL_CACHE_NUM = params -> {
        String modelName = (String) params[0];
        String useCache = (String) params[1];
        Integer count = (Integer) params[2];

        MetricHelper helper = MetricHelper.build().name("model_use_cache_num")
                .tag("model", modelName)
                .tag("useCache", useCache);
        if (params.length >= 4) {
            Map<String, String> tmpMap = (Map<String, String>) params[3];
            if (MapUtils.isNotEmpty(tmpMap)) {
                Map<String, String> bizTags = new HashMap<>(tmpMap);
                bizTags.remove("traceId");
                bizTags.forEach(helper::tag);
            }
        }
        helper.count(count);
    };

    public static Item MODEL_4LEVEL_SECOND_CACHE_NUM = params -> {
        String modelName = (String) params[0];
        String useCache = (String) params[1];
        Integer count = (Integer) params[2];

        MetricHelper helper = MetricHelper.build().name("model_4level_use_cache_num")
                .tag("model", modelName)
                .tag("useCache", useCache);
        if (params.length >= 4) {
            Map<String, String> tmpMap = (Map<String, String>) params[3];
            if (MapUtils.isNotEmpty(tmpMap)) {
                Map<String, String> bizTags = new HashMap<>(tmpMap);
                bizTags.remove("traceId");
                bizTags.forEach(helper::tag);
            }
        }
        helper.count(count);
    };

    /**
     * 预计算查询结果行数上报
     * params[0]:bizCode
     * params[1]:单实体数据行数
     */
    public static Item Prediction_ResultRowNum = params -> {
        String bizCode = (String) params[0];
        int rowNum = ((Number) params[1]).intValue();

        MetricHelper helper = MetricHelper.build().name("Prediction_ResultRowNum")
                .tag("bizCode", bizCode);
        helper.count(rowNum);
    };

    /**
     * 预计算查询结果字段数上报
     * params[0]:bizCode
     * params[1]:单实体单行字段数量
     */
    public static Item Prediction_ResultRowValNum = params -> {
        String bizCode = (String) params[0];
        int rowNum = ((Number) params[1]).intValue();

        MetricHelper helper = MetricHelper.build().name("Prediction_ResultRowValNum")
                .tag("bizCode", bizCode);
        helper.count(rowNum);
    };

    /**
     * 预测慢请求
     */
    public static Item Predict_SlowRequest = params -> {
        MetricHelper helper = MetricHelper.build().name("Predict_SlowRequest")
                .tag("bizCode", (String) params[0]);
        helper.count();
    };

    /**
     * metric上报
     */
    public static Item Log_Metric = params -> {
        String name = (String) params[0];
        Map<String, Object> tags = (Map<String, Object>) params[1];
        Integer count = (Integer) params[2];

        MetricHelper helper = MetricHelper.build().name(name);

        for (Map.Entry<String, Object> entry : tags.entrySet()) {
            if (entry.getValue() != null) {
                helper.tag(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }

        helper.count(count);
    };


    public static Logger logger = LoggerFactory.getLogger(RaptorTrack.class);


    private static void logEvent(String type, Object... params) {
        switch (params.length) {
            case 1: {
                Cat.logEvent(type, String.valueOf(params[0]));
                return;
            }
            case 2: {
                for (int i = 0; i < ((Number) params[1]).intValue(); i++) {
                    Cat.logEvent(type, String.valueOf(params[0]));
                }
                return;
            }
            case 3: {
                Cat.logEvent(type, String.valueOf(params[0]), String.valueOf(params[1]), null);
                return;
            }
            default: {
            }
        }
    }

    public static <T> T take(String type, String name, Supplier<T> metric) {
        return take(type, name, Collections.emptyMap(), metric);
    }

    public static <T> T take(String type, String name, Map<String, Object> appends, Supplier<T> metric) {
        Transaction t = Cat.newTransaction(type, name);
        if (MapUtils.isNotEmpty(appends)) {
            appends.forEach(t::addData);
        }
        try {
            T r = metric.get();
            t.setSuccessStatus();
            return r;
        } catch (Exception e) {
            t.setStatus(e);
            throw e;
        } finally {
            t.complete();
        }
    }


    @FunctionalInterface
    public interface Item {
        void report(Object... params);
    }

}
