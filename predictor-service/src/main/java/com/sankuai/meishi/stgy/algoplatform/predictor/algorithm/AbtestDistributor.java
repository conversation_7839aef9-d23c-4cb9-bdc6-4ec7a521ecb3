package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Preconditions;
import com.google.common.hash.Hashing;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.nio.charset.Charset;
import java.util.List;

/**
 * AB测试分流器
 */
@Data
public class AbtestDistributor {
    private String strategyName;
    private List<DistributionConfig> distributions;

    public static AbtestDistributor getInstance(String jsonStr) {
        AbtestDistributor distributor = JSONObject.parseObject(jsonStr, AbtestDistributor.class, Feature.OrderedField);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(distributor.getDistributions()), "策略列表为空:%s", distributor.getStrategyName());
        Preconditions.checkArgument(distributor.getDistributions().stream().mapToInt(DistributionConfig::getQuota).sum() == 100,
                "流量配置异常:%s", distributor.getStrategyName());
        return distributor;
    }

    public String getStrategyName() {
        return strategyName;
    }

    public DistributionConfig getDistributionConfig(String abtestKey) {
        if (this.distributions.size() == 1) {
            return this.distributions.get(0);
        }
        int location = Math.abs(Hashing.md5().hashString(abtestKey, Charset.defaultCharset()).asInt() % 100);
        int left = 0;
        for (DistributionConfig config : this.distributions) {
            if (left <= location && location < left + config.getQuota()) {
                return config;
            } else {
                left += config.getQuota();
            }
        }
        // 兜底，默认第一个策略
        RaptorTrack.Sys_UnexpectedVisitNum.report("unknownAbtestStrategy");
        return this.distributions.get(0);
    }

    @Data
    public static class DistributionConfig {
        private String name;
        private Long strategyId;
        private Integer quota;
    }
}
