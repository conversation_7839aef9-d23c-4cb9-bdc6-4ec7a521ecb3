package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import org.apache.commons.lang3.StringUtils;
import py4j.reflection.CurrentThreadClassLoadingStrategy;


/**
 * python调用java时的定义类加载策略
 */
public class CustomThreadClassLoadingStrategy extends CurrentThreadClassLoadingStrategy {

    /**
     * 默认策略，每次访问会重新加载，并发加载时存在竞争，会导致线程block。
     * 重写类加载方法，如果是JavaBridge类，则直接返回该类即可，无需重新加载
     * @param className
     * @return
     * @throws ClassNotFoundException
     */
    @Override
    public Class<?> classForName(String className) throws ClassNotFoundException {
        if (StringUtils.equals(className, "JavaBridge") ||
                StringUtils.equals(className, JavaBridge.class.getName())) {
            return JavaBridge.class;
        }
        return Class.forName(className, true, this.getClassLoader());
    }

}
