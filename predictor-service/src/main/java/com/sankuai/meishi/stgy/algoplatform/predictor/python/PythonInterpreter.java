package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import com.google.common.base.Preconditions;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.ThreadPoolFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import py4j.GatewayServer;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.sankuai.meishi.stgy.algoplatform.predictor.python.util.LocationUtil.getRuntimePath;

/**
 * Python运行环境
 */
@Slf4j
@Getter
public class PythonInterpreter {
    private final int MAX_CONNECT_TIMEOUT = 60;
    private final String name;
    private final String processName;
    private final List<String> features;
    private int port = 25333;

    private GatewayServer gatewayServer;
    private Process pythonProcess;

    public PythonInterpreter(String name, String processName, List<String> features) {
        this.name = name;
        this.processName = processName;
        this.features = features;
        Runtime.getRuntime().addShutdownHook(new Thread(this::destroy));
    }

    public PythonInterpreter setPort(int port) {
        this.port = port;
        return this;
    }

    /**
     * 运行
     */
    public void run() {
        String runtimePath = getRuntimePath() + "/python";
        log.info("PythonRuntime({}) runtimePath:{}", this.name, runtimePath);
        try {
            Runtime.getRuntime()
                    .exec("chmod 777 run.sh", null, new File(runtimePath)).waitFor();
        } catch (InterruptedException | IOException e) {
            throw new RuntimeException("chmod run.sh " + this.name + " error", e);
        }
        Process process;
        String futureJoin = "\"" + String.join("#", Optional.ofNullable(this.features).orElse(Collections.emptyList())) + "\"";
        try {
            process = Runtime.getRuntime().exec(String.format("./run.sh %s %s %s %s", this.name, this.port, this.processName, futureJoin), null, new File(runtimePath));
        } catch (IOException e) {
            throw new RuntimeException(String.format("run.sh %s %s %s error:", this.name, this.processName, futureJoin), e);
        }
        this.pythonProcess = process;
        ThreadPoolFactory.getPyServerLoggingThreadPool().submit(new PyLogger(process, this.processName));
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.info("PythonRuntime({}) run.sh is called.", this.processName);
    }

    /**
     * 连接
     */
    public void connect() {
        GatewayServer server = new GatewayServer(null, this.port, this.port + 1,
                GatewayServer.DEFAULT_CONNECT_TIMEOUT, GatewayServer.DEFAULT_READ_TIMEOUT, null);
        this.gatewayServer = server;
        server.start();

        for (int i = 0; i < MAX_CONNECT_TIMEOUT; i++) {
            if (heartbeat()) {
                log.info("PythonRuntime({}) connected.", this.processName);
                return;
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        throw new RuntimeException(String.format("连接超时: %s %s", this.processName, this.port));
    }

    /**
     * 重新启动
     */
    public void restart() {
        log.info("restart({}): isAlive:{}", this.processName,
                Optional.ofNullable(this.pythonProcess).map(Process::isAlive).orElse(null));
        if (this.pythonProcess != null) {
            if (this.pythonProcess.isAlive()) {
                log.info("restart({}): destroy", this.getName());
                this.pythonProcess.destroy();
                for (int i = 0; this.pythonProcess.isAlive() && i < 5; i++) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
                if (this.pythonProcess.isAlive()) {
                    log.info("restart({}): destroyForcibly", this.processName);
                    this.pythonProcess.destroyForcibly();
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
                Preconditions.checkState(!this.pythonProcess.isAlive(), "杀死旧进程失败: %s", this.processName);
            }
            this.pythonProcess = null;
        }
        log.info("restart({}): prepare re-run", this.getName());
        run();
    }

    /**
     * 获取Python运行实例
     *
     * @return PythonBridge
     */
    public PythonBridge getInstance() {
        return PythonBridgeProxy.getProxyInstance(this.processName, this.gatewayServer);
    }

    /**
     * 心跳检测
     *
     * @return 是否成功
     */
    public boolean heartbeat() {
        try {
            String hello = ((PythonBridge) this.gatewayServer.getPythonServerEntryPoint(new Class[]{PythonBridge.class}))
                    .say_hello(this.processName);
            return ("hello:" + this.processName).equals(hello);
        } catch (Exception e) {
            log.warn("heartbeat error:{}, ex:", this.processName, e);
        }
        return false;
    }

    /**
     * 进程ID
     *
     * @return 进程ID, 获取不到为null
     */
    public String getPrecessId() {
        if (this.pythonProcess == null) {
            return null;
        }
        try {
            Field pidDeclaredField = this.pythonProcess.getClass().getDeclaredField("pid");
            pidDeclaredField.setAccessible(true);
            long pid = pidDeclaredField.getLong(this.pythonProcess);
            pidDeclaredField.setAccessible(false);
            return String.valueOf(pid);
        } catch (Exception e) {
            log.warn("getPrecessId error:", e);
        }
        return null;
    }

    /**
     * 销毁
     */
    public void destroy() {
        if (this.pythonProcess != null) {
            this.pythonProcess.destroy();
        }
    }

    /**
     * 日志
     */
    private static class PyLogger implements Runnable {
        String name;
        Process pythonPrecess;

        public PyLogger(Process pythonPrecess, String name) {
            this.pythonPrecess = pythonPrecess;
            this.name = name;
        }

        @Override
        public void run() {
            BufferedReader readStdout = new BufferedReader(new InputStreamReader(pythonPrecess.getInputStream()));
            BufferedReader readStderr = new BufferedReader(new InputStreamReader(pythonPrecess.getErrorStream()));

            try {
                String tmp1 = null, tmp2 = null;
                while (pythonPrecess.isAlive()) {
                    while ((tmp1 = readStdout.readLine()) != null || (tmp2 = readStderr.readLine()) != null) {
                        if (tmp1 != null) {
                            log.info("PyEnv({}):{}", this.name, tmp1);
                        }
                        if (tmp2 != null) {
                            log.error("PyEnv({}):{}", this.name, tmp2);
                        }
                    }
                    Thread.sleep(500);
                }
            } catch (Exception e) {
                log.error("PyEnv stdout/sterr occurred error:", e);
            }
            log.info("PyLogger.{} exit.", this.name);
        }
    }
}
