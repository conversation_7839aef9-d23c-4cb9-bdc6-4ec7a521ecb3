package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictionQueryAppService;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictionQueryContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.PredictionDataScriptDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.HorizonStrategyService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.GroovyScriptFactory;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.LogTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack.*;

@Service
@Slf4j
public class PredictionQueryAppServiceImpl implements PredictionQueryAppService {
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Resource
    private PredictionDataScriptDao dataScriptDao;
    @Resource
    private TairClient tairClient;
    @Resource
    private HorizonStrategyService horizonStrategyService;

    @Override
    public PredictionQueryContext queryPredictions(PredictionQueryContext context) {
        return RaptorTrack.take("PredictionQueryAppService", context.getBizCode(),
                ImmutableMap.of("bizCode", context.getBizCode()),
                () -> {
                    try {
                        return doQueryPredictions(context);
                    } finally {
                        LogTools.logContext(context);
                    }
                });
    }

    private PredictionQueryContext doQueryPredictions(PredictionQueryContext context) {
        if (CollectionUtils.isEmpty(context.getEntityIds())) {
            log.info("queryPredictions emptyReq: bizCode:{}", context.getBizCode());
            return context;
        }
        PredictionDataScriptPo scriptPo = dataScriptDao.getValidByCodeWithCache(context.getBizCode());
        if (scriptPo == null) {
            log.warn("queryPredictions emptyScript: bizCode:{}", context.getBizCode());
            RaptorTrack.Sys_UnexpectedVisitNum.report("queryPredictions_emptyScript_" + context.getBizCode());
            return context;
        }
        long t0 = System.currentTimeMillis();
        try {
            if ("groovy".equalsIgnoreCase(scriptPo.getScriptType())) {
                queryPredictionsByGroovyScript(scriptPo.getScript(), context);
            } else {
                throw new IllegalArgumentException("unknown scriptType:" + scriptPo.getScriptType());
            }
        } finally {
            measureAndReport(context);
            log.info("queryPredictions req: bizCode: {}, entitySize:{}, dataSize:{}, cost:{}",
                    context.getBizCode(), context.getEntityIds().size(), context.getData().size(), System.currentTimeMillis() - t0);
        }
        return context;
    }

    private void measureAndReport(PredictionQueryContext context) {
        try {
            // 预计算查询有结果上报
            Prediction_ResultNum.report(context.getBizCode(), context.getEntityIds().size(), context.getData().size());
            for (List<PredictionQueryContext.PredictionValue> list : context.getData().values()) {
                list = Optional.ofNullable(list).orElse(Collections.emptyList());
                // 预计算查询结果行数上报
                Prediction_ResultRowNum.report(context.getBizCode(), list.size());
                for (PredictionQueryContext.PredictionValue val : list) {
                    Integer valSize = Optional.ofNullable(val).map(PredictionQueryContext.PredictionValue::getValues).map(Map::size)
                            .orElse(0);
                    // 预计算查询结果字段数上报
                    Prediction_ResultRowValNum.report(context.getBizCode(), valSize);
                }
            }
        } catch (Exception e) {
            log.error("measureAndReport error: ", e);
        }
    }

    @Override
    public PredictionQueryContext queryPredictionsByGroovyScript(String script, PredictionQueryContext context) {
        Map<String, Object> variableInScript = ImmutableMap.of(
                "context", context,
                "tairClient", tairClient,
                "strategyService", horizonStrategyService,
                "jackson", objectMapper
        );
        Object output = GroovyScriptFactory.runScript(script, variableInScript);
        if (!(output instanceof Map)) {
            throw new IllegalStateException(String.format("返回结果不正确: %s, 实际为: %s", script, output));
        }
        Map<String, Object> m = (Map<String, Object>) output;
        convertOutput(context, m);
        return context;
    }

    private void convertOutput(PredictionQueryContext context, Map<String, Object> outMap) {
        try {
            // data
            Map<String, Object> outData = Optional.ofNullable(outMap.get("data")).map(o -> (Map<String, Object>) o).orElse(Collections.emptyMap());
            for (Map.Entry<String, Object> outDataEntry : outData.entrySet()) {
                String entityId = outDataEntry.getKey();
                List<Object> outValues = Optional.ofNullable(outDataEntry.getValue()).map(o -> (List<Object>) o).orElse(Collections.emptyList());
                List<PredictionQueryContext.PredictionValue> newValues = new ArrayList<>();
                for (Object outVal : outValues) {
                    Map<String, Object> mv = (Map<String, Object>) outVal;
                    Map<String, Object> outValMap = (Map<String, Object>) mv.get("values");

                    PredictionQueryContext.PredictionValue newVal = new PredictionQueryContext.PredictionValue();
                    if (outValMap != null) {
                        outValMap.forEach((k1, v1) -> newVal.getValues().put(k1, v1 != null ? v1.toString() : null));
                    }
                    newValues.add(newVal);
                }
                context.getData().put(entityId, newValues);
            }

            // extra
            Map<String, Object> met = Optional.ofNullable(outMap.get("extra")).map(o -> (Map<String, Object>) o).orElse(Collections.emptyMap());
            met.forEach((outExtraKey, outExtraVal) -> context.getRespExtra().put(outExtraKey, outExtraVal != null ? outExtraVal.toString() : null));
        } catch (RuntimeException e) {
            throw new IllegalStateException(String.format("脚本返回结果不正确: %s", outMap), e);
        }
    }

    @Override
    public Map<String, String> queryScript(List<String> bizCodes) {
        List<PredictionDataScriptPo> dataScriptPos = dataScriptDao.getValidByCode(bizCodes);
        return dataScriptPos.stream().collect(Collectors.toMap(
                PredictionDataScriptPo::getBizCode,
                PredictionDataScriptPo::getScript, (v1, v2) -> v2));
    }
}
