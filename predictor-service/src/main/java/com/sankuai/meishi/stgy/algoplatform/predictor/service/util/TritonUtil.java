package com.sankuai.meishi.stgy.algoplatform.predictor.service.util;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import inference.GrpcService;

import java.nio.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class TritonUtil {
    public static GrpcService.ModelInferRequest.Builder buildTensorByShape(GrpcService.ModelInferRequest.Builder request,
                                                                           LinkedHashMap<String, List<Object>> inputValue, Map<String, List<Integer>> inputShape, Map<String, String> inputType,
                                                                           List<String> output) {

        // input
        AtomicInteger inputIndex = new AtomicInteger();
        inputValue.forEach((k, v) -> {
            // data
            GrpcService.InferTensorContents.Builder input0_data = GrpcService.InferTensorContents.newBuilder();
            addDataContent(inputType.get(k), input0_data, v);
            // shape
            GrpcService.ModelInferRequest.InferInputTensor.Builder input0 = GrpcService.ModelInferRequest.InferInputTensor
                    .newBuilder();
            input0.setName(k);
            input0.setDatatype(convertDataType(inputType.get(k)));
            input0.addAllShape(inputShape.get(k).stream().map(Long::valueOf).collect(Collectors.toList()));
            input0.setContents(input0_data);
            request.addInputs(inputIndex.getAndIncrement(), input0);
        });
        // output
        AtomicInteger outputIndex = new AtomicInteger();
        output.forEach(name -> {
            GrpcService.ModelInferRequest.InferRequestedOutputTensor.Builder output0 = GrpcService.ModelInferRequest.InferRequestedOutputTensor
                    .newBuilder();
            output0.setName(name);
            request.addOutputs(outputIndex.getAndIncrement(), output0);
        });
        return request;
    }

    private static String convertDataType(String dataType) {
        Preconditions.checkNotNull(dataType);
        switch (dataType) {
            case "int": {
                return "INT32";
            }
            case "long": {
                return "INT64";
            }
            case "float": {
                return "FP32";
            }
            case "double": {
                return "FP64";
            }
            case "bytes":
            case "string": {
                return "BYTES";
            }
            default:
                throw new IllegalArgumentException(dataType);
        }
    }

    private static void addDataContent(String dataType, GrpcService.InferTensorContents.Builder builder, List<Object> input) {
        Preconditions.checkNotNull(dataType);
        switch (dataType) {
            case "int": {
                builder.addAllIntContents(input.stream().map(m -> ((Number) m).intValue()).collect(Collectors.toList()));
                break;
            }
            case "long": {
                builder.addAllInt64Contents(input.stream().map(m -> ((Number) m).longValue()).collect(Collectors.toList()));
                break;
            }
            case "float": {
                builder.addAllFp32Contents(input.stream().map(m -> ((Number) m).floatValue()).collect(Collectors.toList()));
                break;
            }
            case "double": {
                builder.addAllFp64Contents(input.stream().map(m -> ((Number) m).doubleValue()).collect(Collectors.toList()));
                break;
            }
            case "bytes":
            case "string": {
                List<ByteString> data = input.stream().map(m -> ByteString.copyFromUtf8((String) m)).collect(Collectors.toList());
                builder.addAllByteContents(data);
                break;
            }
            default:
                throw new IllegalArgumentException(dataType);
        }
    }

    public static LinkedHashMap<String, List<Object>> buildOutputContent(LinkedHashMap<String, String> outputType, List<ByteString> output) {
        LinkedHashMap<String, List<Object>> outputValue = new LinkedHashMap<>();
        AtomicInteger outputIndex = new AtomicInteger();
        outputType.forEach((key, type) -> {
            ByteString content = output.get(outputIndex.getAndIncrement());
            ByteBuffer buffer = content.asReadOnlyByteBuffer().order(ByteOrder.LITTLE_ENDIAN);

            switch (type) {
                case "int": {
                    int[] ints = toArray(buffer.asIntBuffer());
                    outputValue.put(key, new ArrayList<>(Arrays.asList(ints)));
                    return;
                }
                case "long": {
                    long[] longs = toArray(buffer.asLongBuffer());
                    outputValue.put(key, new ArrayList<>(Arrays.asList(longs)));
                    break;
                }
                case "float": {
                    float[] floats = toArray(buffer.asFloatBuffer());
                    outputValue.put(key, new ArrayList<>(Arrays.asList(floats)));
                    break;
                }
                case "double": {
                    double[] doubles = toArray(buffer.asDoubleBuffer());
                    outputValue.put(key, new ArrayList<>(Arrays.asList(doubles)));
                    break;
                }
                case "bytes": {
                    break;
                }
                case "string": {
                    outputValue.put(key, ByteStringParser.extractStringsFromByteString(content, false));
                    break;
                }
                default:
                    throw new IllegalArgumentException(type);
            }
        });
        return outputValue;
    }

    public static int[] toArray(IntBuffer b) {
        if (b.hasArray()) {
            if (b.arrayOffset() == 0)
                return b.array();
            return Arrays.copyOfRange(b.array(), b.arrayOffset(), b.array().length);
        }
        b.rewind();
        int[] tmp = new int[b.remaining()];
        b.get(tmp);
        return tmp;
    }

    public static long[] toArray(LongBuffer b) {
        if (b.hasArray()) {
            if (b.arrayOffset() == 0)
                return b.array();
            return Arrays.copyOfRange(b.array(), b.arrayOffset(), b.array().length);
        }
        b.rewind();
        long[] tmp = new long[b.remaining()];
        b.get(tmp);
        return tmp;
    }

    public static float[] toArray(FloatBuffer b) {
        if (b.hasArray()) {
            if (b.arrayOffset() == 0)
                return b.array();
            return Arrays.copyOfRange(b.array(), b.arrayOffset(), b.array().length);
        }
        b.rewind();
        float[] tmp = new float[b.remaining()];
        b.get(tmp);
        return tmp;
    }

    public static double[] toArray(DoubleBuffer b) {
        if (b.hasArray()) {
            if (b.arrayOffset() == 0)
                return b.array();
            return Arrays.copyOfRange(b.array(), b.arrayOffset(), b.array().length);
        }
        b.rewind();
        double[] tmp = new double[b.remaining()];
        b.get(tmp);
        return tmp;
    }

    public static char[] toArray(CharBuffer b) {
        if (b.hasArray()) {
            if (b.arrayOffset() == 0)
                return b.array();
            return Arrays.copyOfRange(b.array(), b.arrayOffset(), b.array().length);
        }
        b.rewind();
        char[] tmp = new char[b.remaining()];
        b.get(tmp);
        return tmp;
    }
}
