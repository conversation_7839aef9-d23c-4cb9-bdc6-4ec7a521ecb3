package com.sankuai.meishi.stgy.algoplatform.predictor.enumeration;

/**
 * <AUTHOR>
 * @date 2024年05月13日 10:18 上午
 */
public enum ModelType {
    TF(1, "TF模型"),
    TORCH(2, "Torch模型");

    private final Integer code;
    private final String name;

    ModelType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ModelType getModelTypeByCode(Integer code) {
        ModelType[] allTypes = ModelType.values();
        for (ModelType modelType : allTypes) {
            if (modelType.getCode().equals(code)) {
                return modelType;
            }
        }
        return null;
    }
}
