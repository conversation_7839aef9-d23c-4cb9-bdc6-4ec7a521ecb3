package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.sankuai.oceanus.http.client.okhttp.OceanusInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.client.utils.URIBuilder;

import java.util.Map;
import java.util.Objects;

@Slf4j
public class HttpUtil {

    public static String OCTO_APP_KEY = "com.sankuai.octo.openapi";
    private static OkHttpClient client;

    static {
        client = new OkHttpClient.Builder()
                .addInterceptor(new OceanusInterceptor(OCTO_APP_KEY))
                .build();
    }

    /**
     * http get请求
     */
    public static String httpGet(String url, Map<String, String> param, Map<String, String> headerMap) {
        long t = System.currentTimeMillis();
        String resStr = null;
        try {
            if (!MapUtils.isEmpty(param)) {
                URIBuilder builder = new URIBuilder(url);
                param.forEach(builder::setParameter);
                url = builder.build().toString();
            }

            Request.Builder builder = new Request.Builder().url(url);
            if (!MapUtils.isEmpty(headerMap)) {
                headerMap.forEach(builder::addHeader);
            }
            Request request = builder.build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                resStr = Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
            log.error("HttpUtil.httpGet error ", e);
        } finally {
            log.info("HttpUtil.httpGet url:{}, resp:{}, cost:{}", url, resStr, System.currentTimeMillis() - t);
        }
        return resStr;
    }

//    public static void main(String[] args) throws IOException {
//        OkHttpClient client = new OkHttpClient.Builder()
//                .addInterceptor(new OceanusInterceptor(OCTO_APP_KEY))
//                .build();
//        String url = "https://octoopenapi.vip.sankuai.com/v1/provider?appkey=com.sankuai.algoplatform.predictor&env=4&type=1&status=2";
//        Request request = new Request.Builder().url(url)
//                .addHeader(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, OCTO_APP_KEY)
//                .addHeader(HttpHeader.OCEANUS_TARGET_ENV_HEADER, TargetRequestEnv.ONLINE.getDesc())
//                .build();
//        Response response = client.newCall(request).execute();
//        System.out.println("result：" + response.body().string());
//    }
}