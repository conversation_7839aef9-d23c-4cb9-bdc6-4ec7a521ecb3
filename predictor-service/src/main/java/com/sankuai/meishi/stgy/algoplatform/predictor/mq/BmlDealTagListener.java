package com.sankuai.meishi.stgy.algoplatform.predictor.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictResponse;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.TPredictServicePublish;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.DateUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Component
@Slf4j
public class BmlDealTagListener implements IMessageListener<String> {

    @Autowired
    private TPredictServicePublish tPredictServicePublish;

    @Autowired
    @Qualifier("bmlDealTagResultProducer")
    private IProducerProcessor bmlDealTagResultProducer;

    @MdpConfig("bml_deal_tag_predict_retry:2")
    private Integer retry;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext messagetContext) {
        String messageBody = null;
        try {
            messageBody = (String) message.getBody();
            log.info("BmlDealTagListener#recvMessage,body={}", messageBody);
            TPredictRequest tPredictRequest = JSON.parseObject(messageBody, TPredictRequest.class);
            handler(tPredictRequest);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("BmlDealTagListener#recvMessage error: msg:{}, ex:", messageBody, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

    private void handler(TPredictRequest req) throws Exception {
        long timestamp = System.currentTimeMillis();
        try {
            RetryUtil.retry(() -> {
                TPredictResponse response = tPredictServicePublish.predict(req);
                String message = buildMessageByResponse(response, req, timestamp);
                bmlDealTagResultProducer.sendMessage(message);
                RaptorTrack.HANDLE_BML_DEAL_TAG.report("true");
            }, Objects::nonNull, retry, 500L);
        } catch (Exception e) {
            log.error("BmlDealTagListener#handler error", e);
            TPredictResponse response = new TPredictResponse(-1);
            String message = buildMessageByResponse(response, req, timestamp);
            bmlDealTagResultProducer.sendMessage(message);
            RaptorTrack.HANDLE_BML_DEAL_TAG.report("false");
        }
    }

    private String buildMessageByResponse(TPredictResponse resp, TPredictRequest req, long timestamp) {
        // 放额外信息的map
        if (resp.getExtra() == null) {
            resp.setExtra(new HashMap<>());
        }
        resp.getExtra().put("cost", String.valueOf(System.currentTimeMillis() - timestamp));
        Map<String, String> reqExtra = req.getExtra();
        if (MapUtils.isNotEmpty(reqExtra)) {
            resp.getExtra().putAll(reqExtra);
        }
        if (MapUtils.isNotEmpty(resp.getData()) && "0".equals(resp.getData().get("code"))) {
            String data = resp.getData().get("data");
            String replace = data.replace("\"", "'");
            resp.getData().put("data", replace);
        }

        //新增额外信息
        String jsonString = JSON.toJSONString(resp);
        JSONObject jsonObject = JSON.parseObject(jsonString);
        String date = DateUtil.formatDate(new Date(), DateUtil.YYYY_MM_DD_HH_MM_SS_FORMAT);

        String partitionDate = "";
        if (MapUtils.isNotEmpty(reqExtra)) {
            partitionDate = reqExtra.get("partition_date");
            jsonObject.put("poi_id", reqExtra.get("poi_id"));
        }
        jsonObject.put("partition_date", StringUtils.isBlank(partitionDate) ? date : partitionDate);

        jsonObject.put("match_time", date);
        jsonObject.put("match_time_str", date);

        if (req.getReq() != null) {
            String inputData = req.getReq().get("input_data");

            jsonObject.put("req", inputData);

            if (StringUtils.isNotBlank(inputData)) {
                JSONObject input = JSON.parseObject(inputData);
                jsonObject.put("deal_id", input.getString("deal_id"));
                jsonObject.put("tag_type", input.getString("tag_type"));
                String djMtType = input.getString("dj_mt_type");
                if ("dj".equals(djMtType)) {
                    jsonObject.put("dj_mt_type", 1);
                } else if ("mt".equals(djMtType)) {
                    jsonObject.put("dj_mt_type", 2);
                }
            }
        }
        return JSON.toJSONString(jsonObject);
    }
}
