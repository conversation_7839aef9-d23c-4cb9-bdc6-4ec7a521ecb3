package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.google.common.collect.ImmutableMap;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictionQueryContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import static com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys.*;

@Slf4j
public class LogTools {
    private final static Logger predictContextLogger = LoggerFactory.getLogger("scribe");
    private static final Logger modelErrorLogger = LoggerFactory.getLogger("logger_com.sankuai.algoplatform.predictor.model.log");

    public static void logContext(PredictContext context) {
        if (!Lion.getConfigRepository().getBooleanValue(PREDICT_CONTEXT_LOG_SWITCH, true)) {
            return;
        }
        try {
            Map<String, String> logData = new HashMap<String, String>() {
                {
                    put("type", "predict");
                    put("bizCode", context.getBizCode());
                    put("abtestKey", context.getAbtestKey());
                    put("req", JSONObject.toJSONString(context.getReq()));
                    put("reqExtra", JSONObject.toJSONString(context.getReqExtra()));
                    put("resp", JSONObject.toJSONString(context.getResp()));
                    put("respExtra", JSONObject.toJSONString(context.getRespExtra()));
                }
            };
            String log = XMDLogFormat.build().putJson(JSON.toJSONString(logData)).toString();
            predictContextLogger.info(log);
        } catch (Exception e) {
            log.error("logContext error:", e);
        }
    }

    public static void logNoCache(PredictModelDto predictModelDto, Collection<String> noCacheKey) {
        if (!Lion.getConfigRepository().getBooleanValue(FEATURE_NO_CACHE_LOG_SWITCH, false)) {
            return;
        }
        try {
            Map<String, String> logData = new HashMap<String, String>() {
                {
                    put("type", "noCache");
                    put("bizCode", predictModelDto.getModel_name());
                    put("abtestKey", "");
                    put("req", JSONObject.toJSONString(predictModelDto));
                    put("reqExtra", "");
                    put("resp", JSONObject.toJSONString(noCacheKey));
                    put("respExtra", "");
                }
            };
            String log = XMDLogFormat.build().putJson(JSON.toJSONString(logData)).toString();
            predictContextLogger.info(log);
        } catch (Exception e) {
            log.error("logNoCache error:", e);
        }
    }

    public static void logContext(PredictionQueryContext context) {
        if (!Lion.getConfigRepository().getBooleanValue(PREDICT_CONTEXT_LOG_SWITCH, true)) {
            return;
        }
        try {
            String log = XMDLogFormat.build()
                    .putTag("type", "queryPredictions")
                    .putTag("bizCode", context.getBizCode())
                    .putTag("req", JSONObject.toJSONString(context.getEntityIds()))
                    .putTag("reqExtra", JSONObject.toJSONString(context.getReqExtra()))
                    .putTag("resp", JSONObject.toJSONString(context.getData()))
                    .putTag("respExtra", JSONObject.toJSONString(context.getRespExtra()))
                    .toString();
            predictContextLogger.info(log);
        } catch (Exception e) {
            log.error("logContext error:", e);
        }
    }

    public static void logLlmContext(PredictContext context) {
        if (!Lion.getConfigRepository().getBooleanValue(LLM_PREDICT_CONTEXT_LOG_SWITCH, true)) {
            return;
        }
        try {
            Map<String, String> logData = new HashMap<String, String>() {
                {
                    put("type", "llmPredict");
                    put("bizCode", context.getBizCode());
                    put("req", JSONObject.toJSONString(context.getReq()));
                    put("reqExtra", JSONObject.toJSONString(context.getReqExtra()));
                    put("resp", JSONObject.toJSONString(context.getResp()));
                    put("respExtra", JSONObject.toJSONString(context.getRespExtra()));
                }
            };
            String log = XMDLogFormat.build().putJson(JSON.toJSONString(logData)).toString();
            predictContextLogger.info(log);
        } catch (Exception e) {
            log.error("logLlmContext error:", e);
        }
    }

    public static void logModelLog(PredictModelDto modelDto) {

        String log = XMDLogFormat.build().putJson(JSON.toJSONString(modelDto)).toString();
        modelErrorLogger.info(log);
    }
}
