package com.sankuai.meishi.stgy.algoplatform.predictor.service;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.mdpcache.anno.Cached;
import com.meituan.mdp.boot.starter.mdpcache.core.CacheType;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.taobao.tair3.client.Result;
import com.taobao.tair3.client.ResultMap;
import com.taobao.tair3.client.impl.MultiTairClient;
import com.taobao.tair3.client.util.ByteArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TairClient {
    com.taobao.tair3.client.TairClient.TairOption TAIR_OPTION = new com.taobao.tair3.client.TairClient.TairOption(500, (short) 0, 86400);
    @Resource(name = "webStorageCellar")
    private MultiTairClient tairClient;

    public Map<String, String> batchGet(String prefix, Collection<String> keys) {
        return batchGet(prefix, keys, "");
    }

    public Map<String, String> batchGet(String prefix, Collection<String> keys, String suffix) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyMap();
        }
        long t0 = System.currentTimeMillis();
        Map<String, String> keyMap = keys.stream().collect(Collectors.toMap(k -> prefix + k + suffix, Function.identity(), (u, v) -> v));

        ResultMap<ByteArray, Result<byte[]>> resp;
        try {
            resp = tairClient.batchGet(getArea(), keyMap.keySet().stream().map(String::getBytes).collect(Collectors.toList()), TAIR_OPTION);
        } catch (Exception e) {
            throw new RuntimeException(String.format("query tair error: %s", keyMap.keySet()), e);
        }

        Map<String, String> res = new HashMap<>(keys.size());
        for (Result<byte[]> r : resp.values()) {
            if (!r.isOK() || r.getKey() == null || r.getResult() == null || r.getResult().length == 0) {
                continue;
            }
            String originKey = keyMap.get(new String(r.getKey()));
            String value = new String(r.getResult());
            res.put(originKey, value);
        }
        log.info("batchGet: prefix:{}, batchSize:{}, suffix:{}, resultSize:{}, cost:{}", prefix, keys.size(), suffix, res.size(), System.currentTimeMillis() - t0);
        return res;
    }

    @Cached(area = "a1", cacheType = CacheType.LOCAL, timeUnit = TimeUnit.SECONDS, expire = 60, localLimit = 5000)
    public Map<String, String> batchGetWithCache(String prefix, Collection<String> keys) {
        return batchGet(prefix, keys);
    }

    @Cached(area = "a1", cacheType = CacheType.LOCAL, timeUnit = TimeUnit.SECONDS, expire = 60, localLimit = 5000)
    public Map<String, String> batchGetWithCache(String prefix, Collection<String> keys, String suffix) {
        return batchGet(prefix, keys, suffix);
    }

    private short getArea() {
        HostEnv env = MdpContextUtils.getHostEnv();
        if (HostEnv.PROD.equals(env) || HostEnv.STAGING.equals(env)) {
            return 15;
        }
        return 40;
//        return (short) (HostEnv.PROD.equals(MdpContextUtils.getHostEnv()) ? 15 : 40);
    }

    public boolean put(String key, String value, long expireTime, TimeUnit timeUnit) {
        com.taobao.tair3.client.TairClient.TairOption option = new com.taobao.tair3.client.TairClient.TairOption();
        option.setExpireTime((int) timeUnit.toSeconds(expireTime));
        option.setTimeout(500);
        Result<Void> res;
        try {
            res = tairClient.put(getArea(), key.getBytes(), value.getBytes(), option);
            return res.isOK();

        } catch (Exception e) {
            log.error("put error: key:{}, value:{}, ex", key, value, e);
            return false;
        }
    }

    public String get(String key) {
        try {
            Result<byte[]> result = tairClient.get(getArea(), key.getBytes(), TAIR_OPTION);
            if (result.isOK()) {
                return new String(result.getResult());
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("tair#get error", e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Boolean> batchPut(Map<String, String> kvMap, long expireTime, TimeUnit timeUnit) {
        if (MapUtils.isEmpty(kvMap)) {
            return Collections.emptyMap();
        }
        com.taobao.tair3.client.TairClient.TairOption option = new com.taobao.tair3.client.TairClient.TairOption();
        option.setExpireTime((int) timeUnit.toSeconds(expireTime));
        option.setTimeout(500);
        Map<ByteArray, byte[]> kv = new HashMap<>(kvMap.size());
        try {
            kvMap.forEach((k,v) -> {
                if (k == null || v == null) {
                    return;
                }
                ByteArray originKey = new ByteArray(k.getBytes());
                byte[] originVal = v.getBytes();
                kv.put(originKey, originVal);
            });
            ResultMap<ByteArray, Result<Void>> resultMap = tairClient.batchPut(getArea(), kv, option);
            Map<String, Boolean> res = new HashMap<>();
            if (resultMap.isOK()) {
                resultMap.forEach((originKey, result) -> {
                    String key = new String(originKey.getBytes());
                    res.put(key, result.isOK());
                });
                return res;
            }
            RaptorTrack.Sys_UnexpectedVisitNum.report("TairBatchPutFail");
            log.warn("batchPut fail: kvMap:{}, resultMap:{}", kvMap, resultMap);
            return Collections.emptyMap();
        } catch (Exception e) {
            log.error("batchPut error: kvMap:{}, ex", kvMap, e);
            return Collections.emptyMap();
        }
    }
}
