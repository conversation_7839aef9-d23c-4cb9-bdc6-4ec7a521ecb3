package com.sankuai.meishi.stgy.algoplatform.predictor.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BmlMatchSceneExtraConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public BmlMatchSceneExtraConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdIsNull() {
            addCriterion("match_scene_id is null");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdIsNotNull() {
            addCriterion("match_scene_id is not null");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdEqualTo(Long value) {
            addCriterion("match_scene_id =", value, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdNotEqualTo(Long value) {
            addCriterion("match_scene_id <>", value, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdGreaterThan(Long value) {
            addCriterion("match_scene_id >", value, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdGreaterThanOrEqualTo(Long value) {
            addCriterion("match_scene_id >=", value, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdLessThan(Long value) {
            addCriterion("match_scene_id <", value, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdLessThanOrEqualTo(Long value) {
            addCriterion("match_scene_id <=", value, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdIn(List<Long> values) {
            addCriterion("match_scene_id in", values, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdNotIn(List<Long> values) {
            addCriterion("match_scene_id not in", values, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdBetween(Long value1, Long value2) {
            addCriterion("match_scene_id between", value1, value2, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andMatchSceneIdNotBetween(Long value1, Long value2) {
            addCriterion("match_scene_id not between", value1, value2, "matchSceneId");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigIsNull() {
            addCriterion("data_mapping_config is null");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigIsNotNull() {
            addCriterion("data_mapping_config is not null");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigEqualTo(String value) {
            addCriterion("data_mapping_config =", value, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigNotEqualTo(String value) {
            addCriterion("data_mapping_config <>", value, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigGreaterThan(String value) {
            addCriterion("data_mapping_config >", value, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigGreaterThanOrEqualTo(String value) {
            addCriterion("data_mapping_config >=", value, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigLessThan(String value) {
            addCriterion("data_mapping_config <", value, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigLessThanOrEqualTo(String value) {
            addCriterion("data_mapping_config <=", value, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigLike(String value) {
            addCriterion("data_mapping_config like", value, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigNotLike(String value) {
            addCriterion("data_mapping_config not like", value, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigIn(List<String> values) {
            addCriterion("data_mapping_config in", values, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigNotIn(List<String> values) {
            addCriterion("data_mapping_config not in", values, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigBetween(String value1, String value2) {
            addCriterion("data_mapping_config between", value1, value2, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andDataMappingConfigNotBetween(String value1, String value2) {
            addCriterion("data_mapping_config not between", value1, value2, "dataMappingConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigIsNull() {
            addCriterion("result_process_config is null");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigIsNotNull() {
            addCriterion("result_process_config is not null");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigEqualTo(String value) {
            addCriterion("result_process_config =", value, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigNotEqualTo(String value) {
            addCriterion("result_process_config <>", value, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigGreaterThan(String value) {
            addCriterion("result_process_config >", value, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigGreaterThanOrEqualTo(String value) {
            addCriterion("result_process_config >=", value, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigLessThan(String value) {
            addCriterion("result_process_config <", value, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigLessThanOrEqualTo(String value) {
            addCriterion("result_process_config <=", value, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigLike(String value) {
            addCriterion("result_process_config like", value, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigNotLike(String value) {
            addCriterion("result_process_config not like", value, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigIn(List<String> values) {
            addCriterion("result_process_config in", values, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigNotIn(List<String> values) {
            addCriterion("result_process_config not in", values, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigBetween(String value1, String value2) {
            addCriterion("result_process_config between", value1, value2, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andResultProcessConfigNotBetween(String value1, String value2) {
            addCriterion("result_process_config not between", value1, value2, "resultProcessConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigIsNull() {
            addCriterion("deploy_info_config is null");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigIsNotNull() {
            addCriterion("deploy_info_config is not null");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigEqualTo(String value) {
            addCriterion("deploy_info_config =", value, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigNotEqualTo(String value) {
            addCriterion("deploy_info_config <>", value, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigGreaterThan(String value) {
            addCriterion("deploy_info_config >", value, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_info_config >=", value, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigLessThan(String value) {
            addCriterion("deploy_info_config <", value, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigLessThanOrEqualTo(String value) {
            addCriterion("deploy_info_config <=", value, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigLike(String value) {
            addCriterion("deploy_info_config like", value, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigNotLike(String value) {
            addCriterion("deploy_info_config not like", value, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigIn(List<String> values) {
            addCriterion("deploy_info_config in", values, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigNotIn(List<String> values) {
            addCriterion("deploy_info_config not in", values, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigBetween(String value1, String value2) {
            addCriterion("deploy_info_config between", value1, value2, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDeployInfoConfigNotBetween(String value1, String value2) {
            addCriterion("deploy_info_config not between", value1, value2, "deployInfoConfig");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNull() {
            addCriterion("owner is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNotNull() {
            addCriterion("owner is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerEqualTo(String value) {
            addCriterion("owner =", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotEqualTo(String value) {
            addCriterion("owner <>", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThan(String value) {
            addCriterion("owner >", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("owner >=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThan(String value) {
            addCriterion("owner <", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThanOrEqualTo(String value) {
            addCriterion("owner <=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLike(String value) {
            addCriterion("owner like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotLike(String value) {
            addCriterion("owner not like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerIn(List<String> values) {
            addCriterion("owner in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotIn(List<String> values) {
            addCriterion("owner not in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerBetween(String value1, String value2) {
            addCriterion("owner between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotBetween(String value1, String value2) {
            addCriterion("owner not between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}