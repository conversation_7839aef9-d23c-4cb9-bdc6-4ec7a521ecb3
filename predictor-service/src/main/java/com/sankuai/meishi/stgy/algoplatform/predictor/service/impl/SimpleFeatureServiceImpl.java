package com.sankuai.meishi.stgy.algoplatform.predictor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import com.meituan.data.feature.thrift.FeatureService;
import com.meituan.data.feature.thrift.QueryRequest;
import com.meituan.data.feature.thrift.QueryResponse;
import com.meituan.data.feature.thrift.common.ResponseStatus;
import com.meituan.data.feature.thrift.model.FeatureGroupParam;
import com.meituan.data.feature.thrift.model.FeatureValue;
import com.meituan.data.feature.thrift.model.QueryKey;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.CatTransaction;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimpleFeatureService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.groovy.util.ListHashMap;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class SimpleFeatureServiceImpl implements SimpleFeatureService {
    @Resource
    private FeatureService.Iface featureClient;

    @Override
    @CatTransaction(type = "SimpleFeatureService", fieldValAsName = "groupId")
    public List<Map<String, String>> query(String groupId, List<Map<String, String>> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyList();
        }
        if (HostEnv.TEST.equals(MdpContextUtils.getHostEnv())) {
            String cfg = Lion.getConfigRepository().get(LionKeys.TEST_EVN_FEATURE_MOCK_DATA);
            if (StringUtils.isNotBlank(cfg)) {
                JSONObject json = JSONObject.parseObject(cfg);
                if ("on".equals(json.getString("switch"))) {
                    log.info("query using mock:{}", cfg);
                    return json.getObject("mock", new TypeReference<List<Map<String, String>>>() {
                    });
                }
            }
        }
        boolean ignoreError = Lion.getConfigRepository().getBooleanValue(LionKeys.IGNORE_QUERY_FEATURE_EXCEPTION, false);
        boolean isError = false, isWrongCode = false;
        long t0 = System.currentTimeMillis();
        QueryRequest req = buildReqDTO(groupId, keys);
        QueryResponse resp = new QueryResponse(ResponseStatus.SUCCESS);
        try {
            resp = featureClient.query(req);
        } catch (Exception e) {
            if (!ignoreError) {
                throw new RuntimeException(String.format("query error: %s, %s", groupId, keys), e);
            } else {
                log.error("query error:", e);
                isError = true;
            }
        }
        // 特征降级开关
        if (!ignoreError) {
            Preconditions.checkState(ResponseStatus.SUCCESS.equals(resp.getStatus()),
                    "特征返回码不正确: %s, %s, %s", resp.getStatus(), groupId, keys);
        } else {
            isWrongCode = !ResponseStatus.SUCCESS.equals(resp.getStatus());
            if (isWrongCode) {
                log.error("query error:{}", resp);
            }
        }
        if (ignoreError && (isError || isWrongCode)) {
            resp.setFeatureValues(IntStream.range(0, keys.size()).mapToObj(HashMap<String, FeatureValue>::new).collect(Collectors.toList()));
        }
        List<Map<String, String>> res = convertResult(resp.getFeatureValues());
        if (log.isDebugEnabled()) {
            log.debug("SimpleFeatureService.query groupId:{}, keys:{}, size:{}, cost:{}",
                    groupId, keys, res.size(), System.currentTimeMillis() - t0);
        }
        return res;
    }

    private static QueryRequest buildReqDTO(String groupId, List<Map<String, String>> reqKeys) {
        QueryRequest req = new QueryRequest();
        // FeatureGroup
        FeatureGroupParam groupParam = new FeatureGroupParam();
        req.setFeatureGroup(groupParam);
        groupParam.setId(Integer.parseInt(groupId));

        List<QueryKey> queryKeys = new ArrayList<>();
        req.setQueryKeys(queryKeys);
        for (Map<String, String> reqKey : reqKeys) {
            QueryKey k = new QueryKey();
            queryKeys.add(k);
            Map<String, String> keyValue = new HashMap<>();
            k.setKeyName2Value(keyValue);
            keyValue.putAll(reqKey);
            k.setDimensionAlias(String.join("|", reqKey.keySet()));
        }
        return req;
    }

    private static List<Map<String, String>> convertResult(List<Map<String, FeatureValue>> resp) {
        if (CollectionUtils.isEmpty(resp)) {
            return Collections.emptyList();
        }
        return resp.stream()
                .map(m -> {
                    Map<String, String> r = new ListHashMap<>();
                    m.forEach((k, v) -> r.put(k, v.getValue()));
                    return r;
                })
                .collect(Collectors.toList());
    }
}
