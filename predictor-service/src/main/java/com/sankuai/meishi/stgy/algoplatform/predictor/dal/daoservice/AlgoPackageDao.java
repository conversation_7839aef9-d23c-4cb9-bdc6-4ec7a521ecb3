package com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice;

import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoPackageExample;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.AlgoPackageMapper;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class AlgoPackageDao {
    @Resource
    private AlgoPackageMapper algoPackageMapper;

    @CatTransaction(type = "DaoService", name = "AlgoPackageDao.getValidPackages")
    public List<AlgoPackagePo> getValidPackages() {
        AlgoPackageExample example = new AlgoPackageExample();
        example.createCriteria().andStatusEqualTo(0);
        return algoPackageMapper.selectByExample(example);
    }

    @CatTransaction(type = "DaoService", name = "AlgoPackageDao.getById")
    public AlgoPackagePo getById(Long id) {
        return algoPackageMapper.selectByPrimaryKey(id);
    }
}
