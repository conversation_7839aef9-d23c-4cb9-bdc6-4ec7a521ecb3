package com.sankuai.meishi.stgy.algoplatform.predictor.config;

import com.dianping.lion.client.Lion;
import com.meituan.mafka.client.bean.MafkaConsumer;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@Slf4j
public class MafkaHelper {

    private static final String NO_CONSUMER = "no_consumer";

    public static boolean isConsumer(String topic) {
        Map<String, String> consumerConfig = Lion.getConfigRepository().getMap(LionKeys.MAFKA_CONSUMER_CONFIG, String.class);
        if (MapUtils.isEmpty(consumerConfig)) {
            log.info("MafkaHelper#isConsumer mafkaConsumerConfig is null");
            return false;
        }
        String config = consumerConfig.getOrDefault(topic, NO_CONSUMER);
        log.info("MafkaHelper#isConsumer topic={}, config={}", topic, config);
        return !Objects.equals(config, NO_CONSUMER);
    }
}
