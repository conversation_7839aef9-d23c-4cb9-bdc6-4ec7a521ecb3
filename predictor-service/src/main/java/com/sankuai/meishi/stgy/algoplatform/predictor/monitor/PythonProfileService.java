package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreter;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreterFactory;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.ThreadPoolFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.sankuai.meishi.stgy.algoplatform.predictor.python.util.LocationUtil.getRuntimePath;

@Service
@Slf4j
public class PythonProfileService {
    private Map<String, PythonProfilingTask> runningPythonProfiling = new ConcurrentHashMap<>();

    public synchronized PythonProfilingTask recordProfiles(String processName, int duration, boolean force) {
        if (MdpContextUtils.isProdEnv() && !force) {
            return new PythonProfilingTask("profiling is not enabled in prod env, use force to dump.");
        }
        PythonInterpreter interpreter = PythonInterpreterFactory.getRuntimeByProcessName(processName);
        if (interpreter == null) {
            return new PythonProfilingTask("interpreter not found.");
        }
        if (this.runningPythonProfiling.containsKey(processName)) {
            return this.runningPythonProfiling.get(processName);
        }
        String pid = interpreter.getPrecessId();
        if (pid == null) {
            return new PythonProfilingTask("process not found.");
        }
        PythonProfilingTask task = new PythonProfilingTask(interpreter, duration);

        ThreadPoolFactory.getProfilingThreadPool().submit(() -> {
            try {
                this.runningPythonProfiling.put(processName, task);
                task.start();
            } catch (Exception e) {
                log.error("PythonProfilingTask error", e);
            } finally {
                this.runningPythonProfiling.remove(processName);
            }
        });
        return task;
    }


    public File downloadProfiles(String outputId) throws FileNotFoundException {
        File file = new File(System.getProperty("java.io.tmpdir") + File.separator + "PythonProfiling" + File.separator + outputId + ".svg");
        if (!file.exists()) {
            return null;
        }
        return file;
    }

    public List<String> dumpStack(String processName, boolean force) {
        if (MdpContextUtils.isProdEnv() && !force) {
            return Collections.singletonList("profiling is not enabled in prod env, use force to dump.");
        }
        PythonInterpreter interpreter = PythonInterpreterFactory.getRuntimeByProcessName(processName);
        if (interpreter == null) {
            return Collections.singletonList("interpreter not found.");
        }
        String pid = interpreter.getPrecessId();
        if (pid == null) {
            return Collections.singletonList("process not found.");
        }
        try {
            String runtimePath = getRuntimePath() + "/python/" + interpreter.getName() + "/bin";
            Process process = Runtime.getRuntime().exec(String.format("./py-spy dump --pid %s", pid), null, new File(runtimePath));
            process.waitFor();
            List<String> outputs = new ArrayList<>();
            outputs.addAll(IOUtils.readLines(new InputStreamReader(process.getInputStream())));
            outputs.addAll(IOUtils.readLines(new InputStreamReader(process.getErrorStream())));
            return outputs;
        } catch (InterruptedException | IOException e) {
            String msg = String.format("dump stack failed, processName: %s, pid: %s", processName, pid);
            log.warn(msg, e);
            return Collections.singletonList(msg);
        }
    }
}
