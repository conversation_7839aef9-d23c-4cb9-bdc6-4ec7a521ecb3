package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

/**
 * 算法包Service
 */
public interface AlgoPackageService {
    /**
     * 单机算法包更新
     *
     * @param throwError 遇到错误时是否抛出
     * @return 更新数量
     */
    int refresh(boolean throwError);

    /**
     * 根据id获取算法包
     *
     * @param id 算法包id
     * @return 算法包
     */
    AlgoPackage get(Long id);

    /**
     * 根据id加载算法包
     *
     * @param id 算法包id
     */
    void loadPackage(Long id);
}
