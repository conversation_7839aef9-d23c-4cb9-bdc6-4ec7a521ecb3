package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableSet;
import com.meituan.mtrace.Tracer;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreterFactory;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.util.GitUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.util.LocationUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.api.errors.RefAlreadyExistsException;
import org.glassfish.jersey.servlet.spi.FilterUrlMappingsProvider;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * 算法包
 */
@Slf4j
@Data
public class AlgoPackage {
    private Long id;
    private String note;
    private String ownerMis;
    private String runtime;
    private String modulePath;
    private String version;
    private String codeRepo = Constants.ALGO_PACKAGE_GIT_URL;

    public AlgoPackage() {
    }

    public AlgoPackage(Long id, String note, String ownerMis, String runtime, String modulePath, String version, String codeRepo) {
        this.id = id;
        this.note = note;
        this.ownerMis = ownerMis;
        this.runtime = runtime;
        this.modulePath = modulePath;
        this.version = version;
        if (StringUtils.isNotEmpty(codeRepo)) {
            this.codeRepo = codeRepo;
        }

    }

    /**
     * 加载代码到本地
     *
     * @return 状态
     */
    public boolean pullCode() {
        long t0 = System.currentTimeMillis();
        String action = "mkdir";
        try {
            // 目录
            String basePath = LocationUtil.getRuntimePath() + "/python";
            File f = new File(basePath + "/algo_pack");
            if (!f.exists()) {
                Preconditions.checkArgument(f.mkdir(), "创建目录失败:" + f.getAbsolutePath());
            }
            f = new File(basePath + "/algo_pack/" + this.version);
            if (f.exists() && f.isDirectory() && f.listFiles() != null && f.listFiles().length > 0) {
                // 已存在
                log.info("pullCode {} already exists.", this.version);
                action = "existed";
                return true;
            }
            if (!f.exists()) {
                Preconditions.checkArgument(f.mkdir(), "创建目录失败:" + f.getAbsolutePath());
            }

            // clone
            //提取出 ssh://*******************/dcgstgy/algoplat-package.git 中的algoplat-package
            String repoName = codeRepo.substring(codeRepo.lastIndexOf('/') + 1);
            repoName = repoName.replace(".git", "");

            File tmpFile = getTmpFile(basePath + "/tmp/" + repoName);
            if (tmpFile.listFiles() == null || tmpFile.listFiles().length == 0) {
                try {
                    action = "clone";
                    GitUtil.clone(codeRepo, tmpFile.getAbsolutePath());
                } catch (GitAPIException e) {
                    try {
                        FileUtils.deleteDirectory(tmpFile);
                    } catch (IOException ex) {
                        throw new RuntimeException(String.format("删除文件夹失败-clone:%s %s", this.id, this.version), ex);
                    }
                    throw new RuntimeException(String.format("拉取代码失败-clone:%s %s", this.id, this.version), e);
                }
            }


            try {
                FileUtils.copyDirectory(tmpFile.getAbsoluteFile(), f.getAbsoluteFile());
            } catch (IOException e) {
                try {
                    FileUtils.deleteDirectory(f);
                } catch (IOException ex) {
                    throw new RuntimeException(String.format("删除文件夹失败:%s %s", this.id, this.version), ex);
                }
                throw new RuntimeException(String.format("Copy文件夹失败:%s %s", this.id, this.version), e);
            }


            // checkout
            try {
                action = "checkout";
                GitUtil.checkout(f.getAbsolutePath(), this.version);
            } catch (IOException | GitAPIException e) {
                try {
                    FileUtils.deleteDirectory(f);
                } catch (IOException ex) {
                    throw new RuntimeException(String.format("删除文件夹失败-checkout:%s %s", this.id, this.version), ex);
                }
                throw new RuntimeException(String.format("拉取代码失败-checkout:%s %s", this.id, this.version), e);
            }
            action = "success";
            return true;
        } finally {
            log.info("pullCode version:{}, status:{}, cost:{}", this.version, action, System.currentTimeMillis() - t0);
            RaptorTrack.Git_Checkout.report(String.format("%s_%s", this.id, this.version), ImmutableSet.of("existed", "success").contains(action) ? "0" : action, 1);
        }
    }

    private File getTmpFile(String tmpBasePath) {
        String tmp = "";
        File tmpBaseFile = new File(tmpBasePath);
        if (!tmpBaseFile.exists()) {
            Preconditions.checkArgument(tmpBaseFile.mkdirs(), "创建目录失败:" + tmpBaseFile.getAbsolutePath());
        }
        File[] files = tmpBaseFile.listFiles();
        if (files != null) {
            for (File file : files) {
                String name = file.getName();
                if (file.isDirectory() && StringUtils.startsWith(name, "tmp")) {
                    String[] split = StringUtils.split(file.getName(), "-");
                    if (split.length == 2
                            && StringUtils.isNumeric(split[1])
                            && (System.currentTimeMillis() - Long.parseLong(split[1]) <= 60000)) {
                        tmp = tmpBasePath + "/" + name;
                        break;
                    }
                }
            }
        }
        if (StringUtils.isEmpty(tmp)) {
            tmp = tmpBasePath + "/tmp-" + System.currentTimeMillis();
        }
        File f = new File(tmp);
        if (!f.exists()) {
            Preconditions.checkArgument(f.mkdir(), "创建目录失败:" + f.getAbsolutePath());
        }
        return f;
    }

    /**
     * 调用算法包
     *
     * @param entrancePath   执行路径
     * @param entranceMethod 执行方法
     * @param args           执行方法参数
     * @return 结果
     */
    public Map<String, ?> invoke(String entrancePath, String entranceMethod, Map<String, ?> args, Map<String, String> contextData) {
        long t0 = System.currentTimeMillis();

        Map<String, ?> res = RaptorTrack.take("AlgoPackage", String.format("%s#%s#%s#%s", this.version, this.modulePath, entrancePath, entranceMethod),
                () -> {
                    String modulePath = String.format("algo_pack.%s.%s%s",
                            this.version,
                            this.modulePath,
                            (StringUtils.isNotBlank(entrancePath) ? "." + entrancePath : ""));


                    String interpreter = getPythonRuntime(runtime);
                    String r = PythonInterpreterFactory.getRuntime(interpreter).getInstance()
                            .invoke(modulePath, entranceMethod, JSONObject.toJSONString(args), JSON.toJSONString(contextData));
                    return JSONObject.parseObject(r, new TypeReference<Map<String, Object>>() {
                    }, Feature.OrderedField);
                });
        log.info("invoke({}):{}.{}.{}, cost:{}", this.id, this.version, this.modulePath, entranceMethod, System.currentTimeMillis() - t0);
        return res;
    }

    /**
     * @param runtime {"default":"python3.7_common","gray-release-big-request":"pypy3.8_common","gray-release-dapan-cate":"pypy3.8_common"}
     * @return string
     */
    private String getPythonRuntime(String runtime) {
        runtime = StringUtils.trim(runtime);
        if (StringUtils.startsWith(runtime, "{")) {
            Map<String, String> runtimeConfig = JSONObject.parseObject(runtime, new TypeReference<Map<String, String>>() {
            });
            String cell = Tracer.getCell();
            if (StringUtils.isEmpty(cell)) {
                cell = "default";
            }
            String res = runtimeConfig.get(cell);
            if (StringUtils.isEmpty(res)) {
                res = runtimeConfig.get("default");
                log.warn("InterpreterConfig runtime:{}, cell:{}", runtime, cell);
                RaptorTrack.Sys_UnexpectedVisitNum.report("InterpreterConfigError_" + cell);
            }
            return res;
        } else {
            return runtime;
        }
    }
//    public static void main(String[] args) {
//        String config = " {\"default\":\"python3.7_common\",\"gray-release-big-request\":\"pypy3.8_common\",\"gray-release-dapan-cate\":\"pypy3.8_common\"}";
//        String config = "{python3.7_common";
//        System.out.println(new AlgoPackage().getPythonRuntime(config));
//    }

}
